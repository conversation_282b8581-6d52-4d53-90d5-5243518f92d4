ESTADO DE BASE DE DATOS - 2025-05-25_22-40-04
=====================================

Tipo: Almacenamiento en memoria
URL: memory://localhost
Estado: Activo pero no respaldable

NOTA: La base de datos esta configurada para usar almacenamiento en memoria.
Los datos se pierden al reiniciar la aplicacion.

Para habilitar respaldos:
1. Configurar PostgreSQL
2. Cambiar DATABASE_URL a postgresql://...
3. Ejecutar npm run db:push
4. Ejecutar npm run db:backup

Archivos del proyecto respaldados en esta fecha:
- package.json
- .env.example
- Scripts de gestion
