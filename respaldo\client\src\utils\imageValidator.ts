/**
 * Utilidad para validar rutas de imágenes
 * 
 * Este módulo proporciona funciones para verificar la existencia de archivos de imagen
 * y validar las rutas en el catálogo de imágenes.
 */

import imageCatalog from '../assets/image-catalog.json';

/**
 * Verifica si una imagen existe
 * 
 * @param path - Ruta de la imagen a verificar
 * @returns Promise que se resuelve a true si la imagen existe, false en caso contrario
 */
export const checkImageExists = async (path: string): Promise<boolean> => {
  try {
    // Eliminar "src/" del inicio de la ruta si existe
    const normalizedPath = path.startsWith('src/') ? path.substring(4) : path;
    
    // Intentar cargar la imagen
    const response = await fetch(normalizedPath, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error(`Error al verificar la imagen ${path}:`, error);
    return false;
  }
};

/**
 * Verifica todas las imágenes en el catálogo
 * 
 * @returns Promise que se resuelve a un objeto con los resultados de la validación
 */
export const validateCatalogImages = async (): Promise<{
  valid: string[];
  invalid: string[];
  total: number;
}> => {
  const valid: string[] = [];
  const invalid: string[] = [];
  
  // Obtener todas las rutas de imágenes del catálogo
  const imagePaths: string[] = [];
  
  imageCatalog.attractions.forEach(attraction => {
    attraction.images.forEach(image => {
      imagePaths.push(image.path);
    });
  });
  
  // Verificar cada imagen
  await Promise.all(
    imagePaths.map(async (path) => {
      const exists = await checkImageExists(path);
      if (exists) {
        valid.push(path);
      } else {
        invalid.push(path);
        console.error(`Imagen no encontrada: ${path}`);
      }
    })
  );
  
  return {
    valid,
    invalid,
    total: imagePaths.length
  };
};

/**
 * Verifica las imágenes de una atracción específica
 * 
 * @param attractionId - ID de la atracción a verificar
 * @returns Promise que se resuelve a un objeto con los resultados de la validación
 */
export const validateAttractionImages = async (attractionId: string): Promise<{
  valid: string[];
  invalid: string[];
  total: number;
}> => {
  const valid: string[] = [];
  const invalid: string[] = [];
  
  // Buscar la atracción en el catálogo
  const attraction = imageCatalog.attractions.find(attr => attr.id === attractionId);
  
  if (!attraction) {
    console.error(`No se encontró la atracción con ID: ${attractionId}`);
    return { valid, invalid, total: 0 };
  }
  
  // Verificar cada imagen de la atracción
  await Promise.all(
    attraction.images.map(async (image) => {
      const exists = await checkImageExists(image.path);
      if (exists) {
        valid.push(image.path);
      } else {
        invalid.push(image.path);
        console.error(`Imagen no encontrada: ${image.path}`);
      }
    })
  );
  
  return {
    valid,
    invalid,
    total: attraction.images.length
  };
};
