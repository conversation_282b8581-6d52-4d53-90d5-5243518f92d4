# Script de restauración para PostgreSQL
# Autor: Sistema de Seguridad Posada 2.0
# Descripción: Restaura respaldos de la base de datos PostgreSQL

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupFile,
    [string]$BackupPath = "./backups",
    [switch]$Force = $false
)

# Funciones de utilidad
function Show-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Show-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Show-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Show-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

# Verificar que pg_restore esté disponible
if (-not (Get-Command pg_restore -ErrorAction SilentlyContinue)) {
    Show-Error "pg_restore no está disponible. Instala PostgreSQL client tools."
    exit 1
}

# Cargar variables de entorno
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
} else {
    Show-Error "Archivo .env no encontrado"
    exit 1
}

# Verificar DATABASE_URL
$databaseUrl = $env:DATABASE_URL
if (-not $databaseUrl -or $databaseUrl.StartsWith("memory://")) {
    Show-Error "DATABASE_URL no configurado o usando memoria."
    exit 1
}

# Parsear DATABASE_URL
if ($databaseUrl -match "postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)") {
    $dbUser = $matches[1]
    $dbPassword = $matches[2]
    $dbHost = $matches[3]
    $dbPort = $matches[4]
    $dbName = $matches[5]
} else {
    Show-Error "Formato de DATABASE_URL inválido"
    exit 1
}

# Verificar archivo de respaldo
$fullBackupPath = if ([System.IO.Path]::IsPathRooted($BackupFile)) {
    $BackupFile
} else {
    Join-Path $BackupPath $BackupFile
}

if (-not (Test-Path $fullBackupPath)) {
    Show-Error "Archivo de respaldo no encontrado: $fullBackupPath"
    exit 1
}

# Verificar si el archivo está cifrado
$isEncrypted = $fullBackupPath.EndsWith(".enc")
$isCompressed = $fullBackupPath.EndsWith(".gz") -or $fullBackupPath.Contains(".gz")

$workingFile = $fullBackupPath

# Descifrar si es necesario
if ($isEncrypted) {
    Show-Info "Descifrando archivo de respaldo..."
    $keyPath = Join-Path $BackupPath "backup.key"
    
    if (-not (Test-Path $keyPath)) {
        Show-Error "Clave de cifrado no encontrada: $keyPath"
        exit 1
    }
    
    try {
        $keyBytes = [System.IO.File]::ReadAllBytes($keyPath)
        $encryptedBytes = [System.IO.File]::ReadAllBytes($fullBackupPath)
        
        # Extraer IV y datos cifrados
        $aes = [System.Security.Cryptography.Aes]::Create()
        $aes.Key = $keyBytes
        $aes.IV = $encryptedBytes[0..15]
        
        $decryptor = $aes.CreateDecryptor()
        $decryptedBytes = $decryptor.TransformFinalBlock($encryptedBytes, 16, $encryptedBytes.Length - 16)
        
        $workingFile = $fullBackupPath -replace "\.enc$", ""
        [System.IO.File]::WriteAllBytes($workingFile, $decryptedBytes)
        
        Show-Success "Archivo descifrado exitosamente"
        $aes.Dispose()
    } catch {
        Show-Error "Error al descifrar: $($_.Exception.Message)"
        exit 1
    }
}

# Descomprimir si es necesario
if ($isCompressed -and $workingFile.EndsWith(".gz")) {
    Show-Info "Descomprimiendo archivo de respaldo..."
    
    if (Get-Command 7z -ErrorAction SilentlyContinue) {
        $decompressedFile = $workingFile -replace "\.gz$", ""
        7z e "$workingFile" -o"$(Split-Path $decompressedFile)" | Out-Null
        $workingFile = $decompressedFile
        Show-Success "Archivo descomprimido exitosamente"
    } else {
        Show-Error "7-Zip no disponible para descomprimir"
        exit 1
    }
}

# Confirmar restauración
if (-not $Force) {
    Show-Warning "ADVERTENCIA: Esta operación sobrescribirá la base de datos actual."
    Show-Info "Base de datos: $dbName"
    Show-Info "Host: $dbHost:$dbPort"
    Show-Info "Archivo: $BackupFile"
    
    $confirmation = Read-Host "¿Continuar con la restauración? (y/N)"
    if ($confirmation -ne "y" -and $confirmation -ne "Y") {
        Show-Info "Restauración cancelada por el usuario"
        exit 0
    }
}

Show-Info "Iniciando restauración de base de datos..."

# Configurar variables de entorno para pg_restore
$env:PGPASSWORD = $dbPassword

try {
    # Ejecutar pg_restore
    $restoreArgs = @(
        "-h", $dbHost,
        "-p", $dbPort,
        "-U", $dbUser,
        "-d", $dbName,
        "--verbose",
        "--clean",
        "--if-exists",
        "--create",
        "$workingFile"
    )

    Show-Info "Ejecutando pg_restore..."
    $process = Start-Process -FilePath "pg_restore" -ArgumentList $restoreArgs -Wait -PassThru -NoNewWindow

    if ($process.ExitCode -eq 0) {
        Show-Success "Restauración completada exitosamente"
    } else {
        Show-Error "Error en pg_restore (código de salida: $($process.ExitCode))"
        exit 1
    }
    
} catch {
    Show-Error "Error durante la restauración: $($_.Exception.Message)"
    exit 1
} finally {
    # Limpiar variable de entorno de contraseña
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
    
    # Limpiar archivos temporales
    if ($isEncrypted -and (Test-Path $workingFile) -and $workingFile -ne $fullBackupPath) {
        Remove-Item $workingFile -Force
        Show-Info "Archivo temporal eliminado"
    }
}

Show-Success "Restauración de base de datos completada"
