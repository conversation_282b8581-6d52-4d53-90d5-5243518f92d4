import bcrypt from 'bcryptjs';
import { type Request, type Response, type NextFunction } from 'express';
import { getStorage } from './storage';
import { type User, type InsertUser } from '@shared/schema';

// Extender el tipo Request para incluir user
declare global {
  namespace Express {
    interface Request {
      user?: User;
    }
  }
}

// Configuración de bcrypt
const SALT_ROUNDS = 12;

/**
 * Hashea una contraseña usando bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  try {
    return await bcrypt.hash(password, SALT_ROUNDS);
  } catch (error) {
    console.error('Error al hashear contraseña:', error);
    throw new Error('Error al procesar la contraseña');
  }
}

/**
 * Verifica una contraseña contra su hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    return await bcrypt.compare(password, hash);
  } catch (error) {
    console.error('Error al verificar contraseña:', error);
    return false;
  }
}

/**
 * Autentica un usuario con username y password
 */
export async function authenticateUser(username: string, password: string): Promise<User | null> {
  try {
    const storage = await getStorage();
    const user = await storage.getUserByUsername(username);
    
    if (!user) {
      return null;
    }
    
    const isValidPassword = await verifyPassword(password, user.password);
    if (!isValidPassword) {
      return null;
    }
    
    // Retornar usuario sin la contraseña
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  } catch (error) {
    console.error('Error en autenticación:', error);
    return null;
  }
}

/**
 * Crea un nuevo usuario (solo para administradores)
 */
export async function createUser(userData: { username: string; password: string }): Promise<User | null> {
  try {
    const storage = await getStorage();
    
    // Verificar si el usuario ya existe
    const existingUser = await storage.getUserByUsername(userData.username);
    if (existingUser) {
      throw new Error('El usuario ya existe');
    }
    
    // Hashear la contraseña
    const hashedPassword = await hashPassword(userData.password);
    
    // Crear el usuario
    const newUserData: InsertUser = {
      username: userData.username,
      password: hashedPassword
    };
    
    const createdUser = await storage.createUser(newUserData);
    
    // Retornar usuario sin la contraseña
    const { password: _, ...userWithoutPassword } = createdUser;
    return userWithoutPassword as User;
  } catch (error) {
    console.error('Error al crear usuario:', error);
    return null;
  }
}

/**
 * Middleware para verificar autenticación
 */
export function requireAuth(req: Request, res: Response, next: NextFunction) {
  if (!req.session?.userId) {
    return res.status(401).json({
      message: 'Acceso no autorizado. Inicia sesión para continuar.'
    });
  }
  
  next();
}

/**
 * Middleware para cargar información del usuario autenticado
 */
export async function loadUser(req: Request, res: Response, next: NextFunction) {
  if (req.session?.userId) {
    try {
      const storage = await getStorage();
      const user = await storage.getUser(req.session.userId);
      
      if (user) {
        // Agregar usuario al request sin la contraseña
        const { password: _, ...userWithoutPassword } = user;
        req.user = userWithoutPassword as User;
      }
    } catch (error) {
      console.error('Error al cargar usuario:', error);
      // No fallar la request, solo no cargar el usuario
    }
  }
  
  next();
}

/**
 * Middleware opcional de autenticación (no falla si no está autenticado)
 */
export function optionalAuth(req: Request, res: Response, next: NextFunction) {
  // Este middleware simplemente carga el usuario si está autenticado
  // pero no requiere autenticación
  loadUser(req, res, next);
}

/**
 * Valida la fortaleza de una contraseña
 */
export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('La contraseña debe tener al menos 8 caracteres');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('La contraseña debe contener al menos una letra mayúscula');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('La contraseña debe contener al menos una letra minúscula');
  }
  
  if (!/[0-9]/.test(password)) {
    errors.push('La contraseña debe contener al menos un número');
  }
  
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('La contraseña debe contener al menos un carácter especial');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Valida el formato de username
 */
export function validateUsername(username: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (username.length < 3) {
    errors.push('El nombre de usuario debe tener al menos 3 caracteres');
  }
  
  if (username.length > 50) {
    errors.push('El nombre de usuario no puede tener más de 50 caracteres');
  }
  
  if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
    errors.push('El nombre de usuario solo puede contener letras, números, guiones y guiones bajos');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
