# Script de Configuración de Autenticación - La Posada del Oso
# Descripción: Configura el sistema de autenticación y crea el primer usuario administrador

Write-Host "🔐 CONFIGURACIÓN DE AUTENTICACIÓN - LA POSADA DEL OSO" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host ""

# Función para mostrar información
function Show-Info {
    param([string]$Message)
    Write-Host "ℹ️ $Message" -ForegroundColor Blue
}

# Función para mostrar advertencias
function Show-Warning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

# Función para mostrar éxito
function Show-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

# Función para mostrar errores
function Show-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

# Verificar que el servidor esté funcionando
Show-Info "Verificando que el servidor esté ejecutándose..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5080/api/auth/me" -Method GET -UseBasicParsing -TimeoutSec 5 2>$null
    Show-Success "Servidor detectado en http://localhost:5080"
} catch {
    Show-Warning "El servidor no está ejecutándose en http://localhost:5080"
    Write-Host ""
    Write-Host "📋 INSTRUCCIONES:" -ForegroundColor Yellow
    Write-Host "1. Abre otra terminal"
    Write-Host "2. Ejecuta 'npm run dev' para iniciar el servidor"
    Write-Host "3. Espera a que el servidor esté listo"
    Write-Host "4. Ejecuta este script nuevamente"
    Write-Host ""
    exit 1
}

Write-Host ""
Write-Host "👤 CREACIÓN DE USUARIO ADMINISTRADOR" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Solicitar información del usuario administrador
$username = Read-Host "Nombre de usuario administrador (mínimo 3 caracteres)"
if ([string]::IsNullOrWhiteSpace($username) -or $username.Length -lt 3) {
    Show-Error "El nombre de usuario debe tener al menos 3 caracteres"
    exit 1
}

Write-Host ""
Write-Host "📋 REQUISITOS DE CONTRASEÑA:" -ForegroundColor Yellow
Write-Host "- Mínimo 8 caracteres"
Write-Host "- Al menos una letra mayúscula"
Write-Host "- Al menos una letra minúscula"
Write-Host "- Al menos un número"
Write-Host "- Al menos un carácter especial (!@#$%^&*()_+-=[]{}|;':\",./<>?)"
Write-Host ""

$password = Read-Host "Contraseña" -AsSecureString
$passwordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($password))

if ([string]::IsNullOrWhiteSpace($passwordPlain) -or $passwordPlain.Length -lt 8) {
    Show-Error "La contraseña debe tener al menos 8 caracteres"
    exit 1
}

$confirmPassword = Read-Host "Confirmar contraseña" -AsSecureString
$confirmPasswordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($confirmPassword))

if ($passwordPlain -ne $confirmPasswordPlain) {
    Show-Error "Las contraseñas no coinciden"
    exit 1
}

Write-Host ""
Show-Info "Creando usuario administrador..."

# Crear el usuario mediante la API
try {
    $body = @{
        username = $username
        password = $passwordPlain
    } | ConvertTo-Json

    $headers = @{
        'Content-Type' = 'application/json'
    }

    $response = Invoke-RestMethod -Uri "http://localhost:5080/api/auth/register" -Method POST -Body $body -Headers $headers -TimeoutSec 10

    Show-Success "Usuario administrador creado exitosamente"
    Write-Host "ID de usuario: $($response.user.id)" -ForegroundColor Green
    Write-Host "Nombre de usuario: $($response.user.username)" -ForegroundColor Green
} catch {
    $errorMessage = $_.Exception.Message
    if ($_.Exception.Response) {
        try {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd() | ConvertFrom-Json
            $errorMessage = $errorBody.message
            if ($errorBody.errors) {
                $errorMessage += ": " + ($errorBody.errors -join ", ")
            }
        } catch {
            # Si no se puede leer la respuesta, usar el mensaje original
        }
    }
    Show-Error "Error al crear usuario: $errorMessage"
    exit 1
}

# Probar el login
Write-Host ""
Show-Info "Probando inicio de sesión..."
try {
    $loginBody = @{
        username = $username
        password = $passwordPlain
    } | ConvertTo-Json

    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5080/api/auth/login" -Method POST -Body $loginBody -Headers $headers -SessionVariable session -TimeoutSec 10

    Show-Success "Inicio de sesión exitoso"
    
    # Probar el endpoint /me
    $meResponse = Invoke-RestMethod -Uri "http://localhost:5080/api/auth/me" -Method GET -WebSession $session -TimeoutSec 10
    Show-Success "Verificación de sesión exitosa"
    
    # Cerrar sesión
    $logoutResponse = Invoke-RestMethod -Uri "http://localhost:5080/api/auth/logout" -Method POST -WebSession $session -TimeoutSec 10
    Show-Success "Cierre de sesión exitoso"
    
} catch {
    Show-Warning "Error al probar el sistema de autenticación, pero el usuario fue creado"
}

# Verificar configuración de SESSION_SECRET
Write-Host ""
Show-Info "Verificando configuración de seguridad..."

if (Test-Path ".env") {
    $envContent = Get-Content ".env" -Raw
    if ($envContent -match "SESSION_SECRET=.*") {
        $sessionSecret = ($envContent | Select-String "SESSION_SECRET=(.+)" | ForEach-Object { $_.Matches[0].Groups[1].Value })
        if ($sessionSecret -eq "tu_clave_secreta_muy_larga_y_segura_aqui" -or $sessionSecret -eq "fallback-secret-key-change-in-production") {
            Show-Warning "SESSION_SECRET usa un valor por defecto. Cambia esto en producción."
        } else {
            Show-Success "SESSION_SECRET configurado correctamente"
        }
    } else {
        Show-Warning "SESSION_SECRET no encontrado en .env"
    }
} else {
    Show-Warning "Archivo .env no encontrado"
}

Write-Host ""
Write-Host "🎉 CONFIGURACIÓN DE AUTENTICACIÓN COMPLETADA" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host ""
Show-Success "Sistema de autenticación configurado exitosamente"
Write-Host ""
Write-Host "📋 ENDPOINTS DISPONIBLES:" -ForegroundColor Cyan
Write-Host "POST /api/auth/login    - Iniciar sesión"
Write-Host "POST /api/auth/logout   - Cerrar sesión"
Write-Host "GET  /api/auth/me       - Verificar estado de autenticación"
Write-Host "POST /api/auth/register - Crear nuevo usuario (desarrollo)"
Write-Host ""
Write-Host "🔧 CREDENCIALES CREADAS:" -ForegroundColor Yellow
Write-Host "Usuario: $username"
Write-Host "Contraseña: [OCULTA]"
Write-Host ""
Write-Host "⚠️ IMPORTANTE PARA PRODUCCIÓN:" -ForegroundColor Red
Write-Host "1. Cambia SESSION_SECRET en .env"
Write-Host "2. Desactiva la ruta /api/auth/register en producción"
Write-Host "3. Configura HTTPS para proteger las sesiones"
Write-Host "4. Considera implementar 2FA para mayor seguridad"
Write-Host ""

# Limpiar variables de contraseña
$passwordPlain = $null
$confirmPasswordPlain = $null
