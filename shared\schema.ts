import { pgTable, text, serial, integer, boolean, date, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Users table (keeping for compatibility)
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = {
  username: string;
  password: string;
};
export type User = typeof users.$inferSelect;

// Contact form submissions table
export const contacts = pgTable("contacts", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  phone: text("phone"),
  subject: text("subject").notNull(),
  message: text("message").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertContactSchema = createInsertSchema(contacts).omit({
  id: true,
  createdAt: true,
});

export type InsertContact = {
  name: string;
  email: string;
  phone?: string | null;
  subject: string;
  message: string;
};
export type Contact = typeof contacts.$inferSelect;

// Booking requests table
// Definiciones de tipos para los campos jsonb
export const GuestsSchema = z.object({
  adults: z.number(),
  children: z.number(),
});
export type GuestsType = z.infer<typeof GuestsSchema>;

export const ContactInfoSchema = z.object({
  fullName: z.string(),
  email: z.string(),
  phone: z.string(),
});
export type ContactInfoType = z.infer<typeof ContactInfoSchema>;

export const PricingSchema = z.object({
  pricePerNight: z.number(),
  nightsCount: z.number(),
  totalPrice: z.number(),
});
export type PricingType = z.infer<typeof PricingSchema>;

export const bookings = pgTable("bookings", {
  id: serial("id").primaryKey(),
  checkIn: date("check_in").notNull(),
  checkOut: date("check_out").notNull(),
  configurationType: text("configuration_type").notNull(),
  guests: jsonb("guests").$type<GuestsType>().notNull(),
  contactInfo: jsonb("contact_info").$type<ContactInfoType>().notNull(),
  specialRequests: text("special_requests"),
  pricing: jsonb("pricing").$type<PricingType>().notNull(),
  status: text("status").notNull().default("pending"), // pending, confirmed, canceled, completed
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const insertBookingSchema = createInsertSchema(bookings).omit({
  id: true,
  createdAt: true,
});

export type InsertBooking = {
  checkIn: string;
  checkOut: string;
  configurationType: string;
  guests: GuestsType;
  contactInfo: ContactInfoType;
  specialRequests?: string | null;
  pricing: PricingType;
  status?: string;
};
export type Booking = typeof bookings.$inferSelect;

// Definir la interfaz IStorage aquí para que pueda ser importada por server/storage.ts
export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  createContact(contact: InsertContact): Promise<Contact>;
  createBooking(booking: InsertBooking): Promise<Booking>;
}
