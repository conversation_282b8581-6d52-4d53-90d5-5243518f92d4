# Script de Configuración de PostgreSQL - La Posada del Oso
# Descripción: Ayuda a configurar PostgreSQL para el proyecto

Write-Host "🐘 CONFIGURACIÓN DE POSTGRESQL - LA POSADA DEL OSO" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""

# Función para mostrar información
function Show-Info {
    param([string]$Message)
    Write-Host "ℹ️ $Message" -ForegroundColor Blue
}

# Función para mostrar advertencias
function Show-Warning {
    param([string]$Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

# Función para mostrar éxito
function Show-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

# Verificar si PostgreSQL está instalado
Show-Info "Verificando instalación de PostgreSQL..."
try {
    $pgVersion = psql --version 2>$null
    if ($pgVersion) {
        Show-Success "PostgreSQL encontrado: $pgVersion"
    } else {
        throw "PostgreSQL no encontrado"
    }
} catch {
    Show-Warning "PostgreSQL no está instalado o no está en el PATH"
    Write-Host ""
    Write-Host "📥 INSTALACIÓN DE POSTGRESQL:" -ForegroundColor Cyan
    Write-Host "1. Descarga PostgreSQL desde: https://www.postgresql.org/download/"
    Write-Host "2. Instala PostgreSQL con las opciones por defecto"
    Write-Host "3. Anota la contraseña del usuario 'postgres'"
    Write-Host "4. Asegúrate de que PostgreSQL esté en el PATH del sistema"
    Write-Host ""
    Write-Host "🔄 Ejecuta este script nuevamente después de la instalación"
    exit 1
}

Write-Host ""
Write-Host "🗄️ CONFIGURACIÓN DE BASE DE DATOS" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Solicitar información de conexión
$dbHost = Read-Host "Host de PostgreSQL (por defecto: localhost)"
if ([string]::IsNullOrWhiteSpace($dbHost)) { $dbHost = "localhost" }

$dbPort = Read-Host "Puerto de PostgreSQL (por defecto: 5432)"
if ([string]::IsNullOrWhiteSpace($dbPort)) { $dbPort = "5432" }

$dbUser = Read-Host "Usuario de PostgreSQL (por defecto: postgres)"
if ([string]::IsNullOrWhiteSpace($dbUser)) { $dbUser = "postgres" }

$dbPassword = Read-Host "Contraseña de PostgreSQL" -AsSecureString
$dbPasswordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($dbPassword))

$dbName = Read-Host "Nombre de la base de datos (por defecto: posada_db)"
if ([string]::IsNullOrWhiteSpace($dbName)) { $dbName = "posada_db" }

# Construir URL de conexión
$databaseUrl = "postgresql://${dbUser}:${dbPasswordPlain}@${dbHost}:${dbPort}/${dbName}"

Write-Host ""
Show-Info "Probando conexión a PostgreSQL..."

# Probar conexión
try {
    $env:PGPASSWORD = $dbPasswordPlain
    $testConnection = psql -h $dbHost -p $dbPort -U $dbUser -d postgres -c "SELECT 1;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Show-Success "Conexión a PostgreSQL exitosa"
    } else {
        throw "Error de conexión"
    }
} catch {
    Show-Warning "No se pudo conectar a PostgreSQL con las credenciales proporcionadas"
    Write-Host "Verifica que:"
    Write-Host "- PostgreSQL esté ejecutándose"
    Write-Host "- Las credenciales sean correctas"
    Write-Host "- El host y puerto sean accesibles"
    exit 1
}

# Crear base de datos si no existe
Show-Info "Verificando/creando base de datos '$dbName'..."
try {
    $createDb = psql -h $dbHost -p $dbPort -U $dbUser -d postgres -c "CREATE DATABASE $dbName;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Show-Success "Base de datos '$dbName' creada exitosamente"
    } else {
        Show-Info "La base de datos '$dbName' ya existe"
    }
} catch {
    Show-Warning "Error al crear la base de datos"
}

# Actualizar archivo .env
Show-Info "Actualizando archivo .env..."
try {
    if (Test-Path ".env") {
        $envContent = Get-Content ".env" -Raw
        $newEnvContent = $envContent -replace "DATABASE_URL=.*", "DATABASE_URL=$databaseUrl"
        Set-Content ".env" $newEnvContent
        Show-Success "Archivo .env actualizado con la nueva configuración de base de datos"
    } else {
        Show-Warning "Archivo .env no encontrado. Creando uno nuevo..."
        Copy-Item ".env.example" ".env"
        $envContent = Get-Content ".env" -Raw
        $newEnvContent = $envContent -replace "DATABASE_URL=.*", "DATABASE_URL=$databaseUrl"
        Set-Content ".env" $newEnvContent
        Show-Success "Archivo .env creado con la configuración de PostgreSQL"
    }
} catch {
    Show-Warning "Error al actualizar archivo .env: $($_.Exception.Message)"
}

# Ejecutar migraciones
Write-Host ""
Show-Info "Ejecutando migraciones de base de datos..."
try {
    npm run db:push
    if ($LASTEXITCODE -eq 0) {
        Show-Success "Migraciones ejecutadas exitosamente"
    } else {
        Show-Warning "Error al ejecutar migraciones"
    }
} catch {
    Show-Warning "Error al ejecutar migraciones: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "🎉 CONFIGURACIÓN COMPLETADA" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Green
Write-Host ""
Show-Success "PostgreSQL configurado exitosamente para La Posada del Oso"
Write-Host ""
Write-Host "📋 PRÓXIMOS PASOS:" -ForegroundColor Cyan
Write-Host "1. Ejecuta 'npm run dev' para iniciar el servidor"
Write-Host "2. El proyecto ahora usará PostgreSQL en lugar de almacenamiento en memoria"
Write-Host "3. Los datos se persistirán entre reinicios del servidor"
Write-Host ""
Write-Host "🔧 CONFIGURACIÓN APLICADA:" -ForegroundColor Yellow
Write-Host "Host: $dbHost"
Write-Host "Puerto: $dbPort"
Write-Host "Usuario: $dbUser"
Write-Host "Base de datos: $dbName"
Write-Host ""

# Limpiar variable de entorno de contraseña
Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
