import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { users, contacts, bookings, type User, type Contact, type Booking, type InsertUser, type InsertContact, type InsertBooking, type IStorage } from "@shared/schema";
import { eq } from "drizzle-orm";

export class PostgresStorage implements IStorage {
  private db: ReturnType<typeof drizzle>;
  private client: postgres.Sql;

  constructor(connectionString: string) {
    // Configurar cliente PostgreSQL con SSL para producción
    const isProduction = process.env.NODE_ENV === 'production';
    
    this.client = postgres(connectionString, {
      ssl: isProduction ? { rejectUnauthorized: false } : false,
      max: 10, // máximo 10 conexiones en el pool
      idle_timeout: 20, // cerrar conexiones inactivas después de 20 segundos
      connect_timeout: 10, // timeout de conexión de 10 segundos
      prepare: false, // desactivar prepared statements para compatibilidad
    });

    this.db = drizzle(this.client);
  }

  async getUser(id: number): Promise<User | undefined> {
    try {
      const result = await this.db
        .select()
        .from(users)
        .where(eq(users.id, id))
        .limit(1);
      
      return result[0];
    } catch (error) {
      console.error('Error al obtener usuario:', error);
      throw new Error('Error al obtener usuario de la base de datos');
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    try {
      const result = await this.db
        .select()
        .from(users)
        .where(eq(users.username, username))
        .limit(1);
      
      return result[0];
    } catch (error) {
      console.error('Error al obtener usuario por nombre:', error);
      throw new Error('Error al obtener usuario de la base de datos');
    }
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    try {
      const result = await this.db
        .insert(users)
        .values(insertUser)
        .returning();
      
      return result[0];
    } catch (error) {
      console.error('Error al crear usuario:', error);
      if (error instanceof Error && error.message.includes('unique')) {
        throw new Error('El nombre de usuario ya existe');
      }
      throw new Error('Error al crear usuario en la base de datos');
    }
  }

  async createContact(insertContact: InsertContact): Promise<Contact> {
    try {
      const result = await this.db
        .insert(contacts)
        .values(insertContact)
        .returning();
      
      return result[0];
    } catch (error) {
      console.error('Error al crear contacto:', error);
      throw new Error('Error al guardar contacto en la base de datos');
    }
  }

  async createBooking(insertBooking: InsertBooking): Promise<Booking> {
    try {
      const result = await this.db
        .insert(bookings)
        .values({
          ...insertBooking,
          status: insertBooking.status || 'pending'
        })
        .returning();
      
      return result[0];
    } catch (error) {
      console.error('Error al crear reserva:', error);
      throw new Error('Error al guardar reserva en la base de datos');
    }
  }

  // Método para verificar la conexión a la base de datos
  async testConnection(): Promise<boolean> {
    try {
      await this.client`SELECT 1`;
      return true;
    } catch (error) {
      console.error('Error de conexión a PostgreSQL:', error);
      return false;
    }
  }

  // Método para cerrar la conexión (útil para testing y shutdown)
  async close(): Promise<void> {
    try {
      await this.client.end();
    } catch (error) {
      console.error('Error al cerrar conexión PostgreSQL:', error);
    }
  }

  // Método para ejecutar migraciones (si es necesario)
  async migrate(): Promise<void> {
    try {
      // Aquí se pueden ejecutar migraciones si es necesario
      console.log('Verificando esquema de base de datos...');
      
      // Verificar que las tablas existen
      const tablesExist = await this.client`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('users', 'contacts', 'bookings')
      `;
      
      if (tablesExist.length < 3) {
        console.warn('⚠️ Algunas tablas no existen. Ejecuta "npm run db:push" para crear el esquema.');
      } else {
        console.log('✅ Esquema de base de datos verificado correctamente.');
      }
    } catch (error) {
      console.error('Error al verificar migraciones:', error);
      throw new Error('Error al verificar esquema de base de datos');
    }
  }
}
