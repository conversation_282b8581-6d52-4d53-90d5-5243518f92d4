# Script de limpieza de logs y archivos temporales
# Autor: Sistema de Gestión Posada 2.0
# Descripción: Limpia logs antiguos, respaldos vencidos y archivos temporales

param(
    [int]$LogRetentionDays = 30,
    [int]$BackupRetentionDays = 90,
    [int]$ReportRetentionDays = 60,
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

# Funciones de utilidad
function Show-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Show-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Show-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Show-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

Write-Host ""
Write-Host "🧹 LIMPIEZA DE ARCHIVOS ANTIGUOS" -ForegroundColor Cyan
Write-Host "=" * 40 -ForegroundColor Cyan

if ($DryRun) {
    Show-Warning "MODO SIMULACIÓN - No se eliminarán archivos"
}

# Verificar que estamos en el directorio correcto
if (-not (Test-Path "package.json")) {
    Show-Error "Este script debe ejecutarse desde el directorio raíz del proyecto"
    exit 1
}

$totalFilesRemoved = 0
$totalSpaceFreed = 0

# 1. Limpiar logs antiguos
Show-Info "Limpiando logs antiguos (más de $LogRetentionDays días)..."

$logPaths = @("logs", "dist/logs")
foreach ($logPath in $logPaths) {
    if (Test-Path $logPath) {
        $oldLogs = Get-ChildItem "$logPath/*.log" -ErrorAction SilentlyContinue | Where-Object { 
            $_.LastWriteTime -lt (Get-Date).AddDays(-$LogRetentionDays) 
        }
        
        if ($oldLogs) {
            foreach ($log in $oldLogs) {
                $fileSize = $log.Length
                if ($Verbose) {
                    Write-Host "    🗑️  $($log.Name) ($([math]::Round($fileSize/1KB, 2)) KB) - $($log.LastWriteTime)"
                }
                
                if (-not $DryRun) {
                    Remove-Item $log.FullName -Force
                }
                
                $totalFilesRemoved++
                $totalSpaceFreed += $fileSize
            }
            
            if (-not $DryRun) {
                Show-Success "Eliminados $($oldLogs.Count) archivos de log de $logPath"
            } else {
                Show-Info "Se eliminarían $($oldLogs.Count) archivos de log de $logPath"
            }
        } else {
            Show-Info "No se encontraron logs antiguos en $logPath"
        }
    }
}

# 2. Limpiar respaldos antiguos
Show-Info "Limpiando respaldos antiguos (más de $BackupRetentionDays días)..."

if (Test-Path "backups") {
    $oldBackups = Get-ChildItem "backups/posada_backup_*" -ErrorAction SilentlyContinue | Where-Object { 
        $_.LastWriteTime -lt (Get-Date).AddDays(-$BackupRetentionDays) 
    }
    
    if ($oldBackups) {
        foreach ($backup in $oldBackups) {
            $fileSize = $backup.Length
            if ($Verbose) {
                Write-Host "    🗑️  $($backup.Name) ($([math]::Round($fileSize/1MB, 2)) MB) - $($backup.LastWriteTime)"
            }
            
            if (-not $DryRun) {
                Remove-Item $backup.FullName -Force
            }
            
            $totalFilesRemoved++
            $totalSpaceFreed += $fileSize
        }
        
        if (-not $DryRun) {
            Show-Success "Eliminados $($oldBackups.Count) respaldos antiguos"
        } else {
            Show-Info "Se eliminarían $($oldBackups.Count) respaldos antiguos"
        }
    } else {
        Show-Info "No se encontraron respaldos antiguos"
    }
} else {
    Show-Info "Directorio de respaldos no existe"
}

# 3. Limpiar reportes antiguos
Show-Info "Limpiando reportes antiguos (más de $ReportRetentionDays días)..."

if (Test-Path "reportes") {
    $oldReports = Get-ChildItem "reportes/*.txt" -ErrorAction SilentlyContinue | Where-Object { 
        $_.LastWriteTime -lt (Get-Date).AddDays(-$ReportRetentionDays) 
    }
    
    if ($oldReports) {
        foreach ($report in $oldReports) {
            $fileSize = $report.Length
            if ($Verbose) {
                Write-Host "    🗑️  $($report.Name) ($([math]::Round($fileSize/1KB, 2)) KB) - $($report.LastWriteTime)"
            }
            
            if (-not $DryRun) {
                Remove-Item $report.FullName -Force
            }
            
            $totalFilesRemoved++
            $totalSpaceFreed += $fileSize
        }
        
        if (-not $DryRun) {
            Show-Success "Eliminados $($oldReports.Count) reportes antiguos"
        } else {
            Show-Info "Se eliminarían $($oldReports.Count) reportes antiguos"
        }
    } else {
        Show-Info "No se encontraron reportes antiguos"
    }
} else {
    Show-Info "Directorio de reportes no existe"
}

# 4. Limpiar archivos temporales de Node.js
Show-Info "Limpiando archivos temporales..."

$tempPaths = @(
    ".vite",
    "dist/.vite",
    "node_modules/.cache",
    ".npm",
    ".cache"
)

foreach ($tempPath in $tempPaths) {
    if (Test-Path $tempPath) {
        $tempFiles = Get-ChildItem $tempPath -Recurse -File -ErrorAction SilentlyContinue | Where-Object { 
            $_.LastWriteTime -lt (Get-Date).AddDays(-7) 
        }
        
        if ($tempFiles) {
            $tempSize = ($tempFiles | Measure-Object -Property Length -Sum).Sum
            
            if ($Verbose) {
                Write-Host "    🗑️  $tempPath ($($tempFiles.Count) archivos, $([math]::Round($tempSize/1MB, 2)) MB)"
            }
            
            if (-not $DryRun) {
                $tempFiles | Remove-Item -Force -ErrorAction SilentlyContinue
            }
            
            $totalFilesRemoved += $tempFiles.Count
            $totalSpaceFreed += $tempSize
        }
    }
}

# 5. Limpiar logs de npm
Show-Info "Limpiando logs de npm..."

$npmLogPath = "$env:APPDATA\npm-cache\_logs"
if (Test-Path $npmLogPath) {
    $npmLogs = Get-ChildItem "$npmLogPath/*.log" -ErrorAction SilentlyContinue | Where-Object { 
        $_.LastWriteTime -lt (Get-Date).AddDays(-14) 
    }
    
    if ($npmLogs) {
        $npmLogSize = ($npmLogs | Measure-Object -Property Length -Sum).Sum
        
        if ($Verbose) {
            Write-Host "    🗑️  Logs de npm ($($npmLogs.Count) archivos, $([math]::Round($npmLogSize/1KB, 2)) KB)"
        }
        
        if (-not $DryRun) {
            $npmLogs | Remove-Item -Force -ErrorAction SilentlyContinue
        }
        
        $totalFilesRemoved += $npmLogs.Count
        $totalSpaceFreed += $npmLogSize
    }
}

# 6. Limpiar archivos de respaldo de configuración antiguos
Show-Info "Limpiando respaldos de configuración antiguos..."

$configBackups = Get-ChildItem ".env.backup.*", "package.json.backup*", "*.backup.*" -ErrorAction SilentlyContinue | Where-Object { 
    $_.LastWriteTime -lt (Get-Date).AddDays(-30) 
}

if ($configBackups) {
    foreach ($backup in $configBackups) {
        $fileSize = $backup.Length
        if ($Verbose) {
            Write-Host "    🗑️  $($backup.Name) ($([math]::Round($fileSize/1KB, 2)) KB) - $($backup.LastWriteTime)"
        }
        
        if (-not $DryRun) {
            Remove-Item $backup.FullName -Force
        }
        
        $totalFilesRemoved++
        $totalSpaceFreed += $fileSize
    }
    
    if (-not $DryRun) {
        Show-Success "Eliminados $($configBackups.Count) respaldos de configuración antiguos"
    } else {
        Show-Info "Se eliminarían $($configBackups.Count) respaldos de configuración antiguos"
    }
}

# 7. Optimizar logs actuales (truncar si son muy grandes)
Show-Info "Verificando tamaño de logs actuales..."

$currentLogs = Get-ChildItem "logs/*.log" -ErrorAction SilentlyContinue
foreach ($log in $currentLogs) {
    $logSizeMB = $log.Length / 1MB
    
    if ($logSizeMB -gt 50) {  # Si el log es mayor a 50MB
        if ($Verbose) {
            Write-Host "    ⚠️  $($log.Name) es muy grande ($([math]::Round($logSizeMB, 2)) MB)"
        }
        
        if (-not $DryRun) {
            # Mantener solo las últimas 1000 líneas
            $content = Get-Content $log.FullName -Tail 1000
            $content | Set-Content $log.FullName
            Show-Warning "Log $($log.Name) truncado a las últimas 1000 líneas"
        } else {
            Show-Info "Se truncaría el log $($log.Name) (muy grande)"
        }
    }
}

# Mostrar resumen
Write-Host ""
Write-Host "📊 RESUMEN DE LIMPIEZA" -ForegroundColor Yellow
Write-Host "=" * 25 -ForegroundColor Yellow

$spaceMB = [math]::Round($totalSpaceFreed / 1MB, 2)
$spaceKB = [math]::Round($totalSpaceFreed / 1KB, 2)

if ($DryRun) {
    Write-Host "🔍 SIMULACIÓN COMPLETADA:" -ForegroundColor Cyan
    Write-Host "  📁 Archivos que se eliminarían: $totalFilesRemoved"
    Write-Host "  💾 Espacio que se liberaría: $spaceMB MB ($spaceKB KB)"
} else {
    Write-Host "✅ LIMPIEZA COMPLETADA:" -ForegroundColor Green
    Write-Host "  📁 Archivos eliminados: $totalFilesRemoved"
    Write-Host "  💾 Espacio liberado: $spaceMB MB ($spaceKB KB)"
}

Write-Host ""
Write-Host "📋 CONFIGURACIÓN UTILIZADA:" -ForegroundColor Yellow
Write-Host "  🗓️  Retención de logs: $LogRetentionDays días"
Write-Host "  🗓️  Retención de respaldos: $BackupRetentionDays días"
Write-Host "  🗓️  Retención de reportes: $ReportRetentionDays días"

if ($totalFilesRemoved -eq 0) {
    Show-Info "No se encontraron archivos para limpiar"
} else {
    if (-not $DryRun) {
        Show-Success "Limpieza completada exitosamente"
    }
}

Write-Host ""
Write-Host "💡 COMANDOS ÚTILES:" -ForegroundColor Cyan
Write-Host "  • Simulación: powershell -ExecutionPolicy Bypass -File scripts/cleanup-logs.ps1 -DryRun"
Write-Host "  • Verbose: powershell -ExecutionPolicy Bypass -File scripts/cleanup-logs.ps1 -Verbose"
Write-Host "  • Personalizado: powershell -ExecutionPolicy Bypass -File scripts/cleanup-logs.ps1 -LogRetentionDays 15"
