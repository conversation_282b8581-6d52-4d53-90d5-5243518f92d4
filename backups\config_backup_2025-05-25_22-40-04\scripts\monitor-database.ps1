# Script de monitoreo de PostgreSQL
# Autor: Sistema de Seguridad Posada 2.0
# Descripción: Monitorea el estado y rendimiento de la base de datos PostgreSQL

param(
    [switch]$Continuous = $false,
    [int]$IntervalSeconds = 60,
    [switch]$Detailed = $false
)

# Funciones de utilidad
function Show-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Show-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Show-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Show-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

# Verificar que psql esté disponible
if (-not (Get-Command psql -ErrorAction SilentlyContinue)) {
    Show-Error "psql no está disponible. Instala PostgreSQL client tools."
    exit 1
}

# Cargar variables de entorno
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
} else {
    Show-Error "Archivo .env no encontrado"
    exit 1
}

# Verificar DATABASE_URL
$databaseUrl = $env:DATABASE_URL
if (-not $databaseUrl -or $databaseUrl.StartsWith("memory://")) {
    Show-Warning "DATABASE_URL no configurado o usando memoria. No se puede monitorear."
    exit 0
}

# Parsear DATABASE_URL
if ($databaseUrl -match "postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)") {
    $dbUser = $matches[1]
    $dbPassword = $matches[2]
    $dbHost = $matches[3]
    $dbPort = $matches[4]
    $dbName = $matches[5]
} else {
    Show-Error "Formato de DATABASE_URL inválido"
    exit 1
}

# Configurar variables de entorno para psql
$env:PGPASSWORD = $dbPassword

function Test-DatabaseConnection {
    try {
        $result = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c "SELECT 1;" -t -A 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Get-DatabaseStats {
    $queries = @{
        "Conexiones activas" = "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';"
        "Conexiones totales" = "SELECT count(*) FROM pg_stat_activity;"
        "Tamaño de BD (MB)" = "SELECT round(pg_database_size('$dbName')/1024/1024, 2);"
        "Tablas principales" = "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';"
        "Última actividad" = "SELECT max(query_start) FROM pg_stat_activity WHERE state = 'active';"
    }
    
    $stats = @{}
    
    foreach ($key in $queries.Keys) {
        try {
            $result = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c $queries[$key] -t -A 2>$null
            if ($LASTEXITCODE -eq 0) {
                $stats[$key] = $result.Trim()
            } else {
                $stats[$key] = "Error"
            }
        } catch {
            $stats[$key] = "Error"
        }
    }
    
    return $stats
}

function Get-DetailedStats {
    $detailedQueries = @{
        "Consultas lentas" = @"
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 5;
"@
        "Bloqueos activos" = @"
SELECT blocked_locks.pid AS blocked_pid,
       blocked_activity.usename AS blocked_user,
       blocking_locks.pid AS blocking_pid,
       blocking_activity.usename AS blocking_user,
       blocked_activity.query AS blocked_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
"@
        "Uso de índices" = @"
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC
LIMIT 10;
"@
    }
    
    $detailedStats = @{}
    
    foreach ($key in $detailedQueries.Keys) {
        try {
            $result = psql -h $dbHost -p $dbPort -U $dbUser -d $dbName -c $detailedQueries[$key] 2>$null
            if ($LASTEXITCODE -eq 0) {
                $detailedStats[$key] = $result
            } else {
                $detailedStats[$key] = "No disponible (extensión pg_stat_statements requerida)"
            }
        } catch {
            $detailedStats[$key] = "Error al obtener datos"
        }
    }
    
    return $detailedStats
}

function Show-MonitoringReport {
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    Write-Host ""
    Write-Host "🔍 REPORTE DE MONITOREO POSTGRESQL - $timestamp" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    # Verificar conexión
    if (Test-DatabaseConnection) {
        Show-Success "Conexión a base de datos: OK"
    } else {
        Show-Error "Conexión a base de datos: FALLO"
        return
    }
    
    # Obtener estadísticas básicas
    $stats = Get-DatabaseStats
    
    Write-Host ""
    Write-Host "📊 ESTADÍSTICAS BÁSICAS:" -ForegroundColor Yellow
    foreach ($key in $stats.Keys) {
        Write-Host "  $key`: $($stats[$key])"
    }
    
    # Verificar alertas
    Write-Host ""
    Write-Host "⚠️  ALERTAS:" -ForegroundColor Yellow
    
    $activeConnections = [int]$stats["Conexiones activas"]
    $totalConnections = [int]$stats["Conexiones totales"]
    
    if ($activeConnections -gt 50) {
        Show-Warning "Alto número de conexiones activas: $activeConnections"
    }
    
    if ($totalConnections -gt 100) {
        Show-Warning "Alto número de conexiones totales: $totalConnections"
    }
    
    $dbSizeMB = [float]$stats["Tamaño de BD (MB)"]
    if ($dbSizeMB -gt 1000) {
        Show-Warning "Base de datos grande: $dbSizeMB MB"
    }
    
    # Estadísticas detalladas si se solicita
    if ($Detailed) {
        $detailedStats = Get-DetailedStats
        
        Write-Host ""
        Write-Host "🔍 ESTADÍSTICAS DETALLADAS:" -ForegroundColor Yellow
        
        foreach ($key in $detailedStats.Keys) {
            Write-Host ""
            Write-Host "  $key`:" -ForegroundColor Cyan
            Write-Host $detailedStats[$key]
        }
    }
    
    Write-Host ""
    Write-Host "=" * 60 -ForegroundColor Cyan
}

# Función principal
function Start-Monitoring {
    Show-Info "Iniciando monitoreo de PostgreSQL"
    Show-Info "Base de datos: $dbName en $dbHost`:$dbPort"
    
    if ($Continuous) {
        Show-Info "Modo continuo activado (intervalo: $IntervalSeconds segundos)"
        Show-Info "Presiona Ctrl+C para detener"
        
        while ($true) {
            Show-MonitoringReport
            Start-Sleep -Seconds $IntervalSeconds
        }
    } else {
        Show-MonitoringReport
    }
}

try {
    Start-Monitoring
} catch {
    Show-Error "Error durante el monitoreo: $($_.Exception.Message)"
} finally {
    # Limpiar variable de entorno de contraseña
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
}
