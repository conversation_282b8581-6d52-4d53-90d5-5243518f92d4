# Script de respaldo automatizado para PostgreSQL
# Autor: Sistema de Seguridad Posada 2.0
# Descripción: Realiza respaldos diarios automatizados de la base de datos PostgreSQL

param(
    [string]$BackupPath = "./backups",
    [switch]$Encrypt = $true,
    [switch]$Compress = $true,
    [int]$RetentionDays = 30
)

# Funciones de utilidad
function Show-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Show-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Show-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Show-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

# Verificar que pg_dump esté disponible
if (-not (Get-Command pg_dump -ErrorAction SilentlyContinue)) {
    Show-Error "pg_dump no está disponible. Instala PostgreSQL client tools."
    exit 1
}

# Cargar variables de entorno
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
} else {
    Show-Error "Archivo .env no encontrado"
    exit 1
}

# Verificar DATABASE_URL
$databaseUrl = $env:DATABASE_URL
if (-not $databaseUrl -or $databaseUrl.StartsWith("memory://")) {
    Show-Warning "DATABASE_URL no configurado o usando memoria. Saltando respaldo."
    exit 0
}

# Parsear DATABASE_URL
if ($databaseUrl -match "postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)") {
    $dbUser = $matches[1]
    $dbPassword = $matches[2]
    $dbHost = $matches[3]
    $dbPort = $matches[4]
    $dbName = $matches[5]
} else {
    Show-Error "Formato de DATABASE_URL inválido"
    exit 1
}

# Crear directorio de respaldos
if (-not (Test-Path $BackupPath)) {
    New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
    Show-Info "Directorio de respaldos creado: $BackupPath"
}

# Generar nombre de archivo con timestamp
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$backupFileName = "posada_backup_$timestamp.sql"
$backupFilePath = Join-Path $BackupPath $backupFileName

Show-Info "Iniciando respaldo de base de datos..."
Show-Info "Base de datos: $dbName"
Show-Info "Host: $dbHost:$dbPort"
Show-Info "Archivo: $backupFileName"

# Configurar variables de entorno para pg_dump
$env:PGPASSWORD = $dbPassword

try {
    # Ejecutar pg_dump
    $dumpArgs = @(
        "-h", $dbHost,
        "-p", $dbPort,
        "-U", $dbUser,
        "-d", $dbName,
        "--verbose",
        "--clean",
        "--if-exists",
        "--create",
        "--format=custom",
        "--file=$backupFilePath"
    )

    Show-Info "Ejecutando pg_dump..."
    $process = Start-Process -FilePath "pg_dump" -ArgumentList $dumpArgs -Wait -PassThru -NoNewWindow

    if ($process.ExitCode -eq 0) {
        Show-Success "Respaldo completado exitosamente"
        
        # Verificar tamaño del archivo
        $fileSize = (Get-Item $backupFilePath).Length
        $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
        Show-Info "Tamaño del respaldo: $fileSizeMB MB"
        
        # Comprimir si se solicita
        if ($Compress) {
            Show-Info "Comprimiendo respaldo..."
            $compressedPath = "$backupFilePath.gz"
            
            # Usar 7-Zip si está disponible, sino usar PowerShell
            if (Get-Command 7z -ErrorAction SilentlyContinue) {
                7z a -tgzip "$compressedPath" "$backupFilePath" | Out-Null
                Remove-Item $backupFilePath
                $backupFilePath = $compressedPath
                Show-Success "Respaldo comprimido: $(Split-Path $compressedPath -Leaf)"
            } else {
                Show-Warning "7-Zip no disponible. Respaldo sin comprimir."
            }
        }
        
        # Cifrar si se solicita
        if ($Encrypt) {
            Show-Info "Cifrando respaldo..."
            $encryptedPath = "$backupFilePath.enc"
            
            # Generar clave de cifrado si no existe
            $keyPath = Join-Path $BackupPath "backup.key"
            if (-not (Test-Path $keyPath)) {
                $key = [System.Security.Cryptography.RNGCryptoServiceProvider]::new()
                $keyBytes = New-Object byte[] 32
                $key.GetBytes($keyBytes)
                [System.IO.File]::WriteAllBytes($keyPath, $keyBytes)
                Show-Info "Clave de cifrado generada: $keyPath"
                Show-Warning "IMPORTANTE: Guarda la clave de cifrado en un lugar seguro"
            }
            
            # Cifrar archivo (implementación básica)
            try {
                $keyBytes = [System.IO.File]::ReadAllBytes($keyPath)
                $fileBytes = [System.IO.File]::ReadAllBytes($backupFilePath)
                
                # Usar AES para cifrado
                $aes = [System.Security.Cryptography.Aes]::Create()
                $aes.Key = $keyBytes
                $aes.GenerateIV()
                
                $encryptor = $aes.CreateEncryptor()
                $encryptedBytes = $encryptor.TransformFinalBlock($fileBytes, 0, $fileBytes.Length)
                
                # Combinar IV y datos cifrados
                $finalBytes = $aes.IV + $encryptedBytes
                [System.IO.File]::WriteAllBytes($encryptedPath, $finalBytes)
                
                Remove-Item $backupFilePath
                $backupFilePath = $encryptedPath
                Show-Success "Respaldo cifrado: $(Split-Path $encryptedPath -Leaf)"
                
                $aes.Dispose()
            } catch {
                Show-Warning "Error al cifrar: $($_.Exception.Message)"
            }
        }
        
        # Limpiar respaldos antiguos
        Show-Info "Limpiando respaldos antiguos (>$RetentionDays días)..."
        $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
        $oldBackups = Get-ChildItem $BackupPath -Filter "posada_backup_*" | Where-Object { $_.CreationTime -lt $cutoffDate }
        
        foreach ($oldBackup in $oldBackups) {
            Remove-Item $oldBackup.FullName -Force
            Show-Info "Eliminado: $($oldBackup.Name)"
        }
        
        Show-Success "Proceso de respaldo completado exitosamente"
        Show-Info "Archivo final: $(Split-Path $backupFilePath -Leaf)"
        
    } else {
        Show-Error "Error en pg_dump (código de salida: $($process.ExitCode))"
        exit 1
    }
    
} catch {
    Show-Error "Error durante el respaldo: $($_.Exception.Message)"
    exit 1
} finally {
    # Limpiar variable de entorno de contraseña
    Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
}

Show-Success "Respaldo de base de datos completado"
