import { Request, Response, NextFunction } from 'express';
import csrf from 'csrf';
import { logCSRFAttempt } from './security-logger';

// Crear instancia de CSRF
const tokens = new csrf();

// Extender las interfaces para incluir CSRF
declare global {
  namespace Express {
    interface Request {
      csrfToken?: () => string;
      csrfSecret?: string;
    }
  }
}

declare module 'express-session' {
  interface SessionData {
    csrfSecret?: string;
  }
}

// Middleware para generar y validar tokens CSRF
export function csrfProtection() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Generar secreto CSRF si no existe en la sesión
    if (!req.session) {
      return res.status(500).json({
        error: 'Sesión requerida para protección CSRF'
      });
    }

    // Generar secreto si no existe
    if (!req.session.csrfSecret) {
      req.session.csrfSecret = tokens.secretSync();
    }

    // Función para generar token CSRF
    req.csrfToken = () => {
      return tokens.create(req.session!.csrfSecret!);
    };

    // Almacenar el secreto en el request para validación
    req.csrfSecret = req.session.csrfSecret;

    // Para métodos seguros (GET, HEAD, OPTIONS), solo generar token
    if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      return next();
    }

    // Para métodos que modifican datos, validar token CSRF
    const token = req.body._csrf ||
                  req.query._csrf ||
                  req.headers['csrf-token'] ||
                  req.headers['x-csrf-token'] ||
                  req.headers['x-xsrf-token'];

    if (!token) {
      logCSRFAttempt(req, { reason: 'missing_token' });
      return res.status(403).json({
        error: 'Token CSRF requerido',
        code: 'CSRF_TOKEN_MISSING'
      });
    }

    // Verificar token
    if (!req.csrfSecret || !tokens.verify(req.csrfSecret, token)) {
      logCSRFAttempt(req, {
        reason: 'invalid_token',
        providedToken: token.substring(0, 10) + '...'
      });
      return res.status(403).json({
        error: 'Token CSRF inválido',
        code: 'CSRF_TOKEN_INVALID'
      });
    }

    // Token válido, continuar
    next();
  };
}

// Middleware para añadir token CSRF a las respuestas
export function csrfTokenMiddleware() {
  return (req: Request, res: Response, next: NextFunction) => {
    // Solo para rutas que necesitan el token
    if (req.path.startsWith('/api/') && req.method === 'GET') {
      // Añadir token CSRF a la respuesta
      const originalJson = res.json;
      res.json = function(body: any) {
        if (req.csrfToken && typeof body === 'object' && body !== null) {
          body.csrfToken = req.csrfToken();
        }
        return originalJson.call(this, body);
      };
    }
    next();
  };
}

// Ruta específica para obtener token CSRF
export function csrfTokenRoute() {
  return (req: Request, res: Response) => {
    if (!req.csrfToken) {
      return res.status(500).json({
        error: 'CSRF protection not initialized'
      });
    }

    res.json({
      csrfToken: req.csrfToken(),
      message: 'Token CSRF generado exitosamente'
    });
  };
}

// Lista de rutas que requieren protección CSRF
export const CSRF_PROTECTED_ROUTES = [
  '/api/contact',
  '/api/booking',
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/logout'
];

// Middleware condicional que solo aplica CSRF a rutas específicas
export function conditionalCSRFProtection() {
  const csrfMiddleware = csrfProtection();

  return (req: Request, res: Response, next: NextFunction) => {
    // Verificar si la ruta requiere protección CSRF
    const requiresCSRF = CSRF_PROTECTED_ROUTES.some(route =>
      req.path.startsWith(route)
    );

    if (requiresCSRF) {
      return csrfMiddleware(req, res, next);
    }

    // Para rutas que no requieren CSRF, solo generar el token si es necesario
    if (!req.session) {
      return next();
    }

    if (!req.session.csrfSecret) {
      req.session.csrfSecret = tokens.secretSync();
    }

    req.csrfToken = () => {
      return tokens.create(req.session!.csrfSecret!);
    };

    next();
  };
}
