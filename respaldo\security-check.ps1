# Script de Verificación de Seguridad - La Posada del Oso
# Autor: Augment Agent
# Descripción: Verifica el estado de seguridad del proyecto

Write-Host "🔒 VERIFICACIÓN DE SEGURIDAD - LA POSADA DEL OSO" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan
Write-Host ""

# Función para mostrar resultados
function Show-Result {
    param(
        [string]$Test,
        [bool]$Passed,
        [string]$Details = ""
    )

    if ($Passed) {
        Write-Host "✅ $Test" -ForegroundColor Green
    } else {
        Write-Host "❌ $Test" -ForegroundColor Red
    }

    if ($Details) {
        Write-Host "   $Details" -ForegroundColor Yellow
    }
    Write-Host ""
}

# 1. Verificar dependencias vulnerables
Write-Host "🔍 Verificando vulnerabilidades de dependencias..." -ForegroundColor Yellow
try {
    $auditResult = npm audit --json 2>$null | ConvertFrom-Json
    $vulnerabilities = $auditResult.metadata.vulnerabilities

    if ($vulnerabilities.total -eq 0) {
        Show-Result "Dependencias sin vulnerabilidades" $true
    } else {
        $critical = $vulnerabilities.critical
        $high = $vulnerabilities.high
        $moderate = $vulnerabilities.moderate
        $low = $vulnerabilities.low

        Show-Result "Dependencias con vulnerabilidades detectadas" $false "Total: $($vulnerabilities.total) (Críticas: $critical, Altas: $high, Moderadas: $moderate, Bajas: $low)"
    }
} catch {
    Show-Result "Error al verificar dependencias" $false "Ejecutar npm audit manualmente"
}

# 2. Verificar archivos de configuración de seguridad
Write-Host "🔧 Verificando configuración de seguridad..." -ForegroundColor Yellow

# Verificar .env.example
$envExampleExists = Test-Path ".env.example"
Show-Result "Archivo .env.example presente" $envExampleExists

# Verificar .gitignore
$gitignoreExists = Test-Path ".gitignore"
if ($gitignoreExists) {
    $gitignoreContent = Get-Content ".gitignore" -Raw
    $hasEnvIgnore = $gitignoreContent -match "\.env"
    $hasCertsIgnore = $gitignoreContent -match "certs/"
    Show-Result "Variables de entorno excluidas de Git" $hasEnvIgnore
    Show-Result "Certificados excluidos de Git" $hasCertsIgnore
} else {
    Show-Result "Archivo .gitignore presente" $false
}

# 3. Verificar certificados SSL
Write-Host "🔐 Verificando certificados SSL..." -ForegroundColor Yellow
$certExists = Test-Path "certs/cert.pem"
$keyExists = Test-Path "certs/key.pem"
Show-Result "Certificado SSL presente" $certExists
Show-Result "Clave privada SSL presente" $keyExists

# 4. Verificar configuración de TypeScript
Write-Host "📝 Verificando compilación TypeScript..." -ForegroundColor Yellow
try {
    $tscResult = npm run check 2>&1
    $tscPassed = $LASTEXITCODE -eq 0
    Show-Result "Compilación TypeScript exitosa" $tscPassed
} catch {
    Show-Result "Error en compilacion TypeScript" $false "Ejecutar npm run check para detalles"
}

# 5. Verificar archivos de seguridad
Write-Host "📋 Verificando documentación de seguridad..." -ForegroundColor Yellow
$securityMdExists = Test-Path "seguridad.md"
Show-Result "Documentación de seguridad presente" $securityMdExists

# 6. Verificar estructura de proyecto
Write-Host "🏗️ Verificando estructura del proyecto..." -ForegroundColor Yellow
$serverExists = Test-Path "server/"
$clientExists = Test-Path "client/"
$sharedExists = Test-Path "shared/"
Show-Result "Estructura de servidor presente" $serverExists
Show-Result "Estructura de cliente presente" $clientExists
Show-Result "Esquemas compartidos presentes" $sharedExists

# Resumen final
Write-Host ""
Write-Host "📊 RESUMEN DE SEGURIDAD" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔴 CRÍTICO: Resolver vulnerabilidades de dependencias antes de producción" -ForegroundColor Red
Write-Host "🟡 IMPORTANTE: Configurar variables de entorno para producción" -ForegroundColor Yellow
Write-Host "🟢 COMPLETADO: Headers de seguridad, rate limiting, validación implementados" -ForegroundColor Green
Write-Host ""
Write-Host "📖 Consulta seguridad.md para más detalles y próximos pasos" -ForegroundColor White
Write-Host ""
