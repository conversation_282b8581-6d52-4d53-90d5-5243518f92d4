import { Request } from 'express';
import { logSuspiciousActivity, logSecurityError } from './security-logger';

// Interfaz para eventos de seguridad
interface SecurityEvent {
  ip: string;
  userAgent: string;
  endpoint: string;
  method: string;
  timestamp: number;
  type: 'failed_auth' | 'validation_error' | 'rate_limit' | 'suspicious_request' | 'csrf_attempt';
  severity: 'low' | 'medium' | 'high' | 'critical';
  details?: any;
}

// Almacén en memoria para eventos de seguridad (en producción usar Redis)
class SecurityEventStore {
  private events: SecurityEvent[] = [];
  private readonly maxEvents = 10000; // Máximo número de eventos en memoria
  private readonly cleanupInterval = 60 * 60 * 1000; // 1 hora

  constructor() {
    // Limpiar eventos antiguos cada hora
    setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  addEvent(event: SecurityEvent) {
    this.events.push(event);
    
    // Mantener solo los eventos más recientes
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }
  }

  getEventsByIP(ip: string, timeWindow: number = 15 * 60 * 1000): SecurityEvent[] {
    const cutoff = Date.now() - timeWindow;
    return this.events.filter(event => 
      event.ip === ip && event.timestamp > cutoff
    );
  }

  getEventsByType(type: SecurityEvent['type'], timeWindow: number = 15 * 60 * 1000): SecurityEvent[] {
    const cutoff = Date.now() - timeWindow;
    return this.events.filter(event => 
      event.type === type && event.timestamp > cutoff
    );
  }

  private cleanup() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); // 24 horas
    this.events = this.events.filter(event => event.timestamp > cutoff);
  }
}

// Instancia global del almacén de eventos
const eventStore = new SecurityEventStore();

// Patrones sospechosos para detectar
const SUSPICIOUS_PATTERNS = {
  // Múltiples intentos de autenticación fallidos
  BRUTE_FORCE_AUTH: {
    type: 'failed_auth' as const,
    threshold: 5,
    timeWindow: 15 * 60 * 1000, // 15 minutos
    severity: 'high' as const
  },
  
  // Múltiples errores de validación
  VALIDATION_FLOOD: {
    type: 'validation_error' as const,
    threshold: 10,
    timeWindow: 5 * 60 * 1000, // 5 minutos
    severity: 'medium' as const
  },
  
  // Múltiples intentos de CSRF
  CSRF_ATTACK: {
    type: 'csrf_attempt' as const,
    threshold: 3,
    timeWindow: 10 * 60 * 1000, // 10 minutos
    severity: 'critical' as const
  },
  
  // Requests sospechosos
  SUSPICIOUS_REQUESTS: {
    type: 'suspicious_request' as const,
    threshold: 20,
    timeWindow: 10 * 60 * 1000, // 10 minutos
    severity: 'high' as const
  }
};

// User-Agents sospechosos
const SUSPICIOUS_USER_AGENTS = [
  /bot/i,
  /crawler/i,
  /spider/i,
  /scraper/i,
  /curl/i,
  /wget/i,
  /python/i,
  /php/i,
  /java/i,
  /go-http-client/i,
  /nikto/i,
  /sqlmap/i,
  /nmap/i,
  /masscan/i,
  /zap/i,
  /burp/i
];

// Endpoints sensibles
const SENSITIVE_ENDPOINTS = [
  '/api/auth',
  '/api/admin',
  '/api/config',
  '/api/users',
  '/.env',
  '/wp-admin',
  '/admin',
  '/phpmyadmin',
  '/config',
  '/backup'
];

// Función para extraer información de la request
function extractRequestInfo(req: Request) {
  return {
    ip: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent') || 'unknown',
    endpoint: req.path,
    method: req.method,
    timestamp: Date.now()
  };
}

// Función para detectar User-Agent sospechoso
function isSuspiciousUserAgent(userAgent: string): boolean {
  return SUSPICIOUS_USER_AGENTS.some(pattern => pattern.test(userAgent));
}

// Función para detectar endpoint sensible
function isSensitiveEndpoint(endpoint: string): boolean {
  return SENSITIVE_ENDPOINTS.some(sensitive => 
    endpoint.toLowerCase().includes(sensitive.toLowerCase())
  );
}

// Función para detectar patrones de ataque
function detectAttackPatterns(ip: string): SecurityEvent['type'][] {
  const detectedPatterns: SecurityEvent['type'][] = [];
  
  Object.entries(SUSPICIOUS_PATTERNS).forEach(([patternName, pattern]) => {
    const events = eventStore.getEventsByIP(ip, pattern.timeWindow);
    const relevantEvents = events.filter(event => event.type === pattern.type);
    
    if (relevantEvents.length >= pattern.threshold) {
      detectedPatterns.push(pattern.type);
    }
  });
  
  return detectedPatterns;
}

// Función principal de monitoreo
export function monitorSecurityEvent(req: Request, eventType: SecurityEvent['type'], details?: any) {
  const requestInfo = extractRequestInfo(req);
  
  // Crear evento de seguridad
  const event: SecurityEvent = {
    ...requestInfo,
    type: eventType,
    severity: 'low',
    details
  };
  
  // Detectar actividad sospechosa
  let suspicious = false;
  
  // Verificar User-Agent sospechoso
  if (isSuspiciousUserAgent(requestInfo.userAgent)) {
    event.severity = 'medium';
    suspicious = true;
    logSuspiciousActivity(req, 'suspicious_user_agent', { userAgent: requestInfo.userAgent });
  }
  
  // Verificar endpoint sensible
  if (isSensitiveEndpoint(requestInfo.endpoint)) {
    event.severity = 'high';
    suspicious = true;
    logSuspiciousActivity(req, 'sensitive_endpoint_access', { endpoint: requestInfo.endpoint });
  }
  
  // Añadir evento al almacén
  eventStore.addEvent(event);
  
  // Detectar patrones de ataque
  const attackPatterns = detectAttackPatterns(requestInfo.ip);
  
  if (attackPatterns.length > 0) {
    event.severity = 'critical';
    suspicious = true;
    
    attackPatterns.forEach(pattern => {
      const patternInfo = Object.entries(SUSPICIOUS_PATTERNS).find(([_, p]) => p.type === pattern);
      if (patternInfo) {
        logSuspiciousActivity(req, `attack_pattern_detected_${pattern}`, {
          pattern: patternInfo[0],
          threshold: patternInfo[1].threshold,
          timeWindow: patternInfo[1].timeWindow,
          recentEvents: eventStore.getEventsByIP(requestInfo.ip, patternInfo[1].timeWindow).length
        }, 'critical');
      }
    });
  }
  
  return {
    suspicious,
    severity: event.severity,
    attackPatterns
  };
}

// Middleware de monitoreo de seguridad
export function securityMonitoringMiddleware() {
  return (req: Request, res: any, next: any) => {
    // Monitorear todas las requests
    const result = monitorSecurityEvent(req, 'suspicious_request');
    
    // Si se detecta actividad altamente sospechosa, podríamos bloquear la request
    if (result.severity === 'critical' && result.attackPatterns.length > 0) {
      // En un entorno de producción, aquí podrías:
      // 1. Bloquear la IP temporalmente
      // 2. Enviar alertas inmediatas
      // 3. Requerir CAPTCHA
      // 4. Aplicar rate limiting más estricto
      
      logSuspiciousActivity(req, 'critical_threat_detected', {
        attackPatterns: result.attackPatterns,
        action: 'request_monitored' // En producción podría ser 'request_blocked'
      }, 'critical');
    }
    
    next();
  };
}

// Funciones específicas para diferentes tipos de eventos
export function monitorAuthFailure(req: Request, details?: any) {
  return monitorSecurityEvent(req, 'failed_auth', details);
}

export function monitorValidationError(req: Request, details?: any) {
  return monitorSecurityEvent(req, 'validation_error', details);
}

export function monitorCSRFAttempt(req: Request, details?: any) {
  return monitorSecurityEvent(req, 'csrf_attempt', details);
}

export function monitorRateLimit(req: Request, details?: any) {
  return monitorSecurityEvent(req, 'rate_limit', details);
}

// Función para obtener estadísticas de seguridad
export function getSecurityStats(timeWindow: number = 60 * 60 * 1000) {
  const stats = {
    totalEvents: 0,
    eventsByType: {} as Record<SecurityEvent['type'], number>,
    topIPs: {} as Record<string, number>,
    topEndpoints: {} as Record<string, number>,
    severityDistribution: {} as Record<SecurityEvent['severity'], number>
  };
  
  const cutoff = Date.now() - timeWindow;
  const recentEvents = eventStore['events'].filter(event => event.timestamp > cutoff);
  
  stats.totalEvents = recentEvents.length;
  
  recentEvents.forEach(event => {
    // Contar por tipo
    stats.eventsByType[event.type] = (stats.eventsByType[event.type] || 0) + 1;
    
    // Contar por IP
    stats.topIPs[event.ip] = (stats.topIPs[event.ip] || 0) + 1;
    
    // Contar por endpoint
    stats.topEndpoints[event.endpoint] = (stats.topEndpoints[event.endpoint] || 0) + 1;
    
    // Contar por severidad
    stats.severityDistribution[event.severity] = (stats.severityDistribution[event.severity] || 0) + 1;
  });
  
  return stats;
}

// Función para inicializar el monitoreo de seguridad
export function initializeSecurityMonitoring() {
  console.log('🔍 Sistema de monitoreo de seguridad inicializado');
  
  // En producción, aquí podrías:
  // 1. Conectar con sistemas de alertas externos
  // 2. Configurar webhooks para notificaciones
  // 3. Integrar con servicios de análisis de seguridad
  // 4. Configurar dashboards de monitoreo
}
