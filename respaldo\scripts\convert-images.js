/**
 * Script Optimizado para Conversión y Gestión de Imágenes WebP
 *
 * Funcionalidades principales:
 * 1. Convierte imágenes JPG, JPEG, PNG y JFIF a formato WebP con calidad 75-85%
 * 2. Optimiza imágenes WebP existentes para reducir tamaño
 * 3. Estandariza nombres de archivo siguiendo el patrón [nombre-atraccion]-[número].webp
 * 4. Actualiza automáticamente el catálogo de imágenes (image-catalog.json)
 * 5. Valida rutas y maneja errores de forma robusta
 * 6. Mantiene estructura de carpetas organizada por atracciones
 * 7. Compatible con múltiples plataformas (Windows, macOS, Linux)
 *
 * <AUTHOR> Agent
 * @version 2.0
 * @date Mayo 2024
 */

import fs from 'fs';
import path from 'path';
import sharp from 'sharp';
import { fileURLToPath } from 'url';

// Obtener el directorio actual en módulos ES
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuración principal
const ASSETS_DIR = path.join(process.cwd(), 'client', 'src', 'assets', 'atracciones');
const CATALOG_PATH = path.join(process.cwd(), 'client', 'src', 'assets', 'image-catalog.json');
const WEBP_QUALITY = 80; // Calidad de las imágenes WebP (75-85% recomendado)
const WEBP_OPTIMIZATION = { quality: WEBP_QUALITY };

// Configuración de dimensiones estándar para el catálogo
const STANDARD_DIMENSIONS = {
  width: 800,
  height: 600
};

// Mapeo de carpetas a nombres estandarizados para archivos
const FOLDER_TO_PREFIX = {
  'cocha-resbaladero': 'cocha-resbaladero',
  'iglesia-san-andres': 'iglesia-san-andres',
  'parque-dinosaurios': 'parque-dinosaurios',
  'pueblo-de-pica': 'pueblo-de-pica',
  'reserva-pampa-del-tamarugal': 'reserva-pampa-del-tamarugal',
  'salar-del-huasco': 'salar-del-huasco'
};

// Mapeo de IDs de carpetas a información de atracciones para el catálogo
const ATTRACTION_INFO = {
  'cocha-resbaladero': {
    title: 'Cocha Resbaladero',
    description: 'Reconocidas piscinas naturales de aguas templadas, ideales para refrescarse y relajarse. Un oasis en el desierto con propiedades terapéuticas.',
    distance: 'A 10 minutos',
    coordinates: { lat: -20.4850, lng: -69.3170 }
  },
  'iglesia-san-andres': {
    title: 'Iglesia de San Andrés de Pica',
    description: 'Monumento histórico nacional construido entre 1880-1886. Destaca por su estilo neoclásico, con un impresionante frontón, torres campanario y una hermosa cúpula central.',
    distance: 'A 8 minutos',
    coordinates: { lat: -20.4917, lng: -69.3294 }
  },
  'parque-dinosaurios': {
    title: 'Valle de los Dinosaurios',
    description: 'Fascinante parque temático con réplicas a tamaño real de dinosaurios. Una experiencia educativa y divertida para toda la familia.',
    distance: 'A 20 minutos',
    coordinates: { lat: -20.5008653, lng: -69.3429026 },
    address: 'Valle de los Dinosaurios Pica (Pintados - Matilla - Pica, Pica, Tarapacá, Chile)'
  },
  'pueblo-de-pica': {
    title: 'Pueblo de Pica',
    description: 'Encantador oasis en el desierto con arquitectura colonial, plazas sombreadas y la famosa Iglesia de San Andrés de Pica.',
    distance: 'A 5 minutos',
    coordinates: { lat: -20.4912, lng: -69.3294 }
  },
  'reserva-pampa-del-tamarugal': {
    title: 'Reserva Pampa del Tamarugal',
    description: 'Bosque único de tamarugos en pleno desierto, hogar de diversas especies y un fenómeno natural sorprendente.',
    distance: 'A 45 minutos',
    coordinates: { lat: -20.4722, lng: -69.6733 }
  },
  'salar-del-huasco': {
    title: 'Salar del Huasco',
    description: 'Impresionante salar ubicado en medio de la cordillera, hogar de flamencos, vicuñas y otras especies. Un paraíso para los amantes de la fotografía.',
    distance: 'A 30 minutos',
    coordinates: { lat: -20.3000, lng: -68.8500 }
  }
};

/**
 * Valida que los directorios necesarios existan
 * @returns {boolean} true si todos los directorios existen
 */
function validateDirectories() {
  try {
    // Verificar directorio de assets
    if (!fs.existsSync(ASSETS_DIR)) {
      console.error(`❌ Error: El directorio de assets no existe: ${ASSETS_DIR}`);
      return false;
    }

    // Verificar directorio del catálogo
    const catalogDir = path.dirname(CATALOG_PATH);
    if (!fs.existsSync(catalogDir)) {
      console.error(`❌ Error: El directorio del catálogo no existe: ${catalogDir}`);
      return false;
    }

    return true;
  } catch (error) {
    console.error('❌ Error al validar directorios:', error.message);
    return false;
  }
}

/**
 * Carga el catálogo de imágenes existente o crea uno nuevo
 * @returns {Object} El catálogo de imágenes
 */
function loadImageCatalog() {
  try {
    if (fs.existsSync(CATALOG_PATH)) {
      const catalogContent = fs.readFileSync(CATALOG_PATH, 'utf8');
      return JSON.parse(catalogContent);
    } else {
      console.log('ℹ️ No se encontró catálogo existente. Se creará uno nuevo.');
      return { attractions: [] };
    }
  } catch (error) {
    console.error('❌ Error al cargar el catálogo:', error.message);
    console.log('ℹ️ Se creará un catálogo nuevo.');
    return { attractions: [] };
  }
}

/**
 * Guarda el catálogo de imágenes actualizado
 * @param {Object} catalog - El catálogo a guardar
 */
function saveImageCatalog(catalog) {
  try {
    const catalogJson = JSON.stringify(catalog, null, 2);
    fs.writeFileSync(CATALOG_PATH, catalogJson, 'utf8');
    console.log('✅ Catálogo de imágenes actualizado correctamente.');
  } catch (error) {
    console.error('❌ Error al guardar el catálogo:', error.message);
  }
}

/**
 * Obtiene todas las imágenes en las carpetas de atracciones
 * @returns {Array} Lista de archivos de imagen con metadatos
 */
async function getImageFiles() {
  const imageFiles = [];

  try {
    // Leer todas las carpetas en el directorio de atracciones
    const folders = fs.readdirSync(ASSETS_DIR).filter(
      folder => fs.statSync(path.join(ASSETS_DIR, folder)).isDirectory()
    );

    // Para cada carpeta, obtener todos los archivos de imagen
    for (const folder of folders) {
      const folderPath = path.join(ASSETS_DIR, folder);

      try {
        const files = fs.readdirSync(folderPath).filter(file => {
          const ext = path.extname(file).toLowerCase();
          return ['.jpg', '.jpeg', '.png', '.webp', '.jfif'].includes(ext);
        });

        // Agregar información de cada archivo
        for (const file of files) {
          const filePath = path.join(folderPath, file);

          try {
            const stats = fs.statSync(filePath);
            imageFiles.push({
              folder,
              file,
              path: filePath,
              size: stats.size,
              extension: path.extname(file).toLowerCase(),
              isWebP: path.extname(file).toLowerCase() === '.webp'
            });
          } catch (fileError) {
            console.warn(`⚠️ Error al leer archivo ${file}: ${fileError.message}`);
          }
        }
      } catch (folderError) {
        console.warn(`⚠️ Error al leer carpeta ${folder}: ${folderError.message}`);
      }
    }

    return imageFiles;
  } catch (error) {
    console.error('❌ Error al obtener archivos de imagen:', error.message);
    return [];
  }
}

/**
 * Genera un nombre de archivo estandarizado
 * @param {string} folder - Nombre de la carpeta
 * @param {string} file - Nombre del archivo original
 * @param {number} index - Índice del archivo
 * @returns {string} Nombre estandarizado del archivo
 */
function generateStandardFileName(folder, file, index) {
  const prefix = FOLDER_TO_PREFIX[folder];
  if (!prefix) {
    console.warn(`⚠️ No se encontró un prefijo para la carpeta: ${folder}`);
    return `${folder}-${index}.webp`;
  }

  return `${prefix}-${index}.webp`;
}

/**
 * Genera descripción alternativa para una imagen
 * @param {string} attractionTitle - Título de la atracción
 * @param {number} index - Índice de la imagen
 * @returns {string} Texto alternativo para la imagen
 */
function generateImageAlt(attractionTitle, index) {
  const altTexts = {
    1: 'Vista principal',
    2: 'Vista panorámica',
    3: 'Detalle arquitectónico',
    4: 'Paisaje circundante',
    5: 'Vista adicional'
  };

  const altText = altTexts[index] || `Vista ${index}`;
  return `${attractionTitle} - ${altText}`;
}

/**
 * Actualiza el catálogo de imágenes con las nuevas imágenes procesadas
 * @param {Array} processedImages - Lista de imágenes procesadas
 */
function updateImageCatalog(processedImages) {
  try {
    console.log('\n📝 Actualizando catálogo de imágenes...');

    // Cargar catálogo existente
    const catalog = loadImageCatalog();

    // Agrupar imágenes por carpeta/atracción
    const imagesByAttraction = {};
    processedImages.forEach(img => {
      if (!imagesByAttraction[img.folder]) {
        imagesByAttraction[img.folder] = [];
      }
      imagesByAttraction[img.folder].push(img);
    });

    // Actualizar o crear entradas para cada atracción
    Object.keys(imagesByAttraction).forEach(folderId => {
      const images = imagesByAttraction[folderId];
      const attractionInfo = ATTRACTION_INFO[folderId];

      if (!attractionInfo) {
        console.warn(`⚠️ No se encontró información para la atracción: ${folderId}`);
        return;
      }

      // Buscar si la atracción ya existe en el catálogo
      let existingAttraction = catalog.attractions.find(attr => attr.id === folderId);

      if (!existingAttraction) {
        // Crear nueva entrada
        existingAttraction = {
          id: folderId,
          title: attractionInfo.title,
          description: attractionInfo.description,
          distance: attractionInfo.distance,
          coordinates: attractionInfo.coordinates,
          images: []
        };
        catalog.attractions.push(existingAttraction);
        console.log(`✅ Nueva atracción añadida al catálogo: ${attractionInfo.title}`);
      }

      // Actualizar imágenes de la atracción
      existingAttraction.images = images.map((img, index) => ({
        path: `src/assets/atracciones/${folderId}/${img.standardFileName}`,
        alt: generateImageAlt(attractionInfo.title, index + 1),
        width: STANDARD_DIMENSIONS.width,
        height: STANDARD_DIMENSIONS.height,
        format: 'webp'
      }));

      console.log(`✅ Actualizada atracción: ${attractionInfo.title} (${images.length} imágenes)`);
    });

    // Guardar catálogo actualizado
    saveImageCatalog(catalog);

  } catch (error) {
    console.error('❌ Error al actualizar el catálogo:', error.message);
  }
}

/**
 * Convierte una imagen a formato WebP
 * @param {string} imagePath - Ruta de la imagen original
 * @param {string} outputPath - Ruta de salida para la imagen WebP
 * @returns {Object} Estadísticas de la conversión
 */
async function convertToWebP(imagePath, outputPath) {
  try {
    // Verificar que el archivo de entrada existe
    if (!fs.existsSync(imagePath)) {
      throw new Error(`El archivo de entrada no existe: ${imagePath}`);
    }

    // Crear directorio de salida si no existe
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Convertir imagen usando Sharp
    await sharp(imagePath)
      .webp(WEBP_OPTIMIZATION)
      .toFile(outputPath);

    // Verificar que la conversión fue exitosa
    if (!fs.existsSync(outputPath)) {
      throw new Error(`No se pudo crear el archivo de salida: ${outputPath}`);
    }

    // Calcular estadísticas
    const inputStats = fs.statSync(imagePath);
    const outputStats = fs.statSync(outputPath);
    const savingsPercent = ((inputStats.size - outputStats.size) / inputStats.size * 100).toFixed(2);

    console.log(`✅ Convertida: ${path.basename(imagePath)} → ${path.basename(outputPath)}`);
    console.log(`   Tamaño original: ${(inputStats.size / 1024).toFixed(2)} KB`);
    console.log(`   Nuevo tamaño: ${(outputStats.size / 1024).toFixed(2)} KB (${savingsPercent}% de ahorro)`);

    return {
      originalSize: inputStats.size,
      newSize: outputStats.size,
      savings: inputStats.size - outputStats.size,
      savingsPercent: parseFloat(savingsPercent)
    };
  } catch (error) {
    console.error(`❌ Error al convertir ${imagePath}:`, error.message);
    throw error;
  }
}

/**
 * Optimiza una imagen WebP existente
 * @param {string} imagePath - Ruta de la imagen WebP original
 * @param {string} outputPath - Ruta de salida para la imagen optimizada
 * @returns {Object} Estadísticas de la optimización
 */
async function optimizeWebP(imagePath, outputPath) {
  try {
    // Verificar que el archivo de entrada existe
    if (!fs.existsSync(imagePath)) {
      throw new Error(`El archivo de entrada no existe: ${imagePath}`);
    }

    // Crear directorio de salida si no existe
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Optimizar imagen usando Sharp
    await sharp(imagePath)
      .webp(WEBP_OPTIMIZATION)
      .toFile(outputPath);

    // Verificar que la optimización fue exitosa
    if (!fs.existsSync(outputPath)) {
      throw new Error(`No se pudo crear el archivo optimizado: ${outputPath}`);
    }

    // Calcular estadísticas
    const inputStats = fs.statSync(imagePath);
    const outputStats = fs.statSync(outputPath);
    const savingsPercent = ((inputStats.size - outputStats.size) / inputStats.size * 100).toFixed(2);

    // Si la imagen optimizada es más grande o igual, mantener la original
    if (outputStats.size >= inputStats.size) {
      fs.unlinkSync(outputPath); // Eliminar la versión optimizada
      console.log(`ℹ️ No se optimizó: ${path.basename(imagePath)} (la versión optimizada no es más pequeña)`);
      return {
        originalSize: inputStats.size,
        newSize: inputStats.size,
        savings: 0,
        savingsPercent: 0
      };
    }

    console.log(`✅ Optimizada: ${path.basename(imagePath)} → ${path.basename(outputPath)}`);
    console.log(`   Tamaño original: ${(inputStats.size / 1024).toFixed(2)} KB`);
    console.log(`   Nuevo tamaño: ${(outputStats.size / 1024).toFixed(2)} KB (${savingsPercent}% de ahorro)`);

    return {
      originalSize: inputStats.size,
      newSize: outputStats.size,
      savings: inputStats.size - outputStats.size,
      savingsPercent: parseFloat(savingsPercent)
    };
  } catch (error) {
    console.error(`❌ Error al optimizar ${imagePath}:`, error.message);
    throw error;
  }
}

/**
 * Función principal del script
 */
async function main() {
  console.log('🚀 Iniciando Script Optimizado de Conversión de Imágenes WebP');
  console.log('================================================================\n');

  // Validar directorios necesarios
  console.log('🔍 Validando directorios...');
  if (!validateDirectories()) {
    console.error('❌ Error: No se pudieron validar los directorios necesarios.');
    process.exit(1);
  }
  console.log('✅ Directorios validados correctamente.\n');

  // Buscar imágenes
  console.log('🔍 Buscando imágenes en las carpetas de atracciones...');
  const imageFiles = await getImageFiles();

  if (imageFiles.length === 0) {
    console.log('ℹ️ No se encontraron imágenes para procesar.');
    return;
  }

  console.log(`📊 Encontradas ${imageFiles.length} imágenes en total.`);
  console.log(`   - ${imageFiles.filter(img => img.isWebP).length} imágenes WebP`);
  console.log(`   - ${imageFiles.filter(img => !img.isWebP).length} imágenes no WebP\n`);

  // Estadísticas de procesamiento
  let totalOriginalSize = 0;
  let totalNewSize = 0;
  let convertedCount = 0;
  let optimizedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;
  const processedImages = [];

  // Procesar cada carpeta
  for (const folder of Object.keys(FOLDER_TO_PREFIX)) {
    const folderPath = path.join(ASSETS_DIR, folder);

    // Verificar si la carpeta existe
    if (!fs.existsSync(folderPath)) {
      console.warn(`⚠️ La carpeta ${folder} no existe. Omitiendo...`);
      continue;
    }

    console.log(`\n📁 Procesando carpeta: ${folder}`);

    // Obtener imágenes de esta carpeta
    const folderImages = imageFiles.filter(img => img.folder === folder);

    if (folderImages.length === 0) {
      console.log(`ℹ️ No se encontraron imágenes en la carpeta ${folder}.`);
      continue;
    }

    // Ordenar imágenes: primero las WebP, luego las no WebP
    folderImages.sort((a, b) => {
      if (a.isWebP && !b.isWebP) return -1;
      if (!a.isWebP && b.isWebP) return 1;
      return a.file.localeCompare(b.file);
    });

    // Procesar imágenes
    let index = 1;
    const processedFiles = new Set();
    const folderProcessedImages = [];

    for (const image of folderImages) {
      // Generar nombre estandarizado
      const standardFileName = generateStandardFileName(folder, image.file, index);
      const outputPath = path.join(folderPath, standardFileName);

      // Evitar procesar el mismo archivo de salida más de una vez
      if (processedFiles.has(standardFileName)) {
        console.log(`⚠️ Ya existe un archivo con el nombre ${standardFileName}. Omitiendo...`);
        skippedCount++;
        continue;
      }

      // Si el archivo de salida ya existe y es el mismo que el de entrada
      if (image.file === standardFileName) {
        console.log(`ℹ️ El archivo ${image.file} ya tiene un nombre estandarizado.`);

        // Si es WebP, intentar optimizarlo
        if (image.isWebP) {
          const tempPath = path.join(folderPath, `temp-${standardFileName}`);
          try {
            const result = await optimizeWebP(image.path, tempPath);

            // Si se optimizó correctamente, reemplazar el original
            if (result.savings > 0) {
              try {
                // En Windows, a veces necesitamos un pequeño retraso antes de renombrar
                if (process.platform === 'win32') {
                  await new Promise(resolve => setTimeout(resolve, 100));
                }

                // Intentar eliminar el archivo original primero
                if (fs.existsSync(image.path)) {
                  fs.unlinkSync(image.path);
                }

                // Renombrar el archivo temporal al nombre original
                fs.renameSync(tempPath, image.path);

                totalOriginalSize += result.originalSize;
                totalNewSize += result.newSize;
                optimizedCount++;

                console.log(`✅ Archivo optimizado reemplazado: ${path.basename(image.path)}`);
              } catch (renameError) {
                console.warn(`⚠️ No se pudo reemplazar el archivo optimizado: ${renameError.message}`);
                // Eliminar archivo temporal si no se pudo renombrar
                if (fs.existsSync(tempPath)) {
                  fs.unlinkSync(tempPath);
                }
                skippedCount++;
              }

              // Añadir a la lista de imágenes procesadas
              folderProcessedImages.push({
                folder,
                standardFileName,
                originalPath: image.path,
                finalPath: image.path
              });
            } else {
              skippedCount++;
              // Aún así, añadir a la lista para el catálogo
              folderProcessedImages.push({
                folder,
                standardFileName,
                originalPath: image.path,
                finalPath: image.path
              });
            }
          } catch (error) {
            console.error(`❌ Error al optimizar ${image.file}:`, error.message);
            // Si hay un error, eliminar el archivo temporal si existe
            if (fs.existsSync(tempPath)) {
              fs.unlinkSync(tempPath);
            }
            errorCount++;
            // Aún así, añadir a la lista para el catálogo
            folderProcessedImages.push({
              folder,
              standardFileName,
              originalPath: image.path,
              finalPath: image.path
            });
          }
        } else {
          skippedCount++;
          // Añadir a la lista para el catálogo (aunque no se procesó)
          folderProcessedImages.push({
            folder,
            standardFileName,
            originalPath: image.path,
            finalPath: image.path
          });
        }

        processedFiles.add(standardFileName);
        index++;
        continue;
      }

      // Procesar la imagen (convertir o optimizar)
      try {
        let result;

        if (image.isWebP) {
          // Optimizar imagen WebP
          result = await optimizeWebP(image.path, outputPath);
          if (result.savings > 0) {
            optimizedCount++;
            // Eliminar archivo original si es diferente al de salida
            if (image.path !== outputPath) {
              try {
                // En Windows, añadir un pequeño retraso antes de eliminar
                if (process.platform === 'win32') {
                  await new Promise(resolve => setTimeout(resolve, 100));
                }
                fs.unlinkSync(image.path);
              } catch (deleteError) {
                console.warn(`⚠️ No se pudo eliminar el archivo original: ${deleteError.message}`);
              }
            }
          } else {
            skippedCount++;
          }
        } else {
          // Convertir a WebP
          result = await convertToWebP(image.path, outputPath);
          convertedCount++;
          // Eliminar archivo original después de conversión exitosa
          try {
            // En Windows, añadir un pequeño retraso antes de eliminar
            if (process.platform === 'win32') {
              await new Promise(resolve => setTimeout(resolve, 100));
            }
            fs.unlinkSync(image.path);
          } catch (deleteError) {
            console.warn(`⚠️ No se pudo eliminar el archivo original: ${deleteError.message}`);
          }
        }

        totalOriginalSize += result.originalSize;
        totalNewSize += result.newSize;

        // Añadir a la lista de imágenes procesadas
        folderProcessedImages.push({
          folder,
          standardFileName,
          originalPath: image.path,
          finalPath: outputPath
        });

        processedFiles.add(standardFileName);
        index++;
      } catch (error) {
        console.error(`❌ Error al procesar ${image.file}:`, error.message);
        errorCount++;
      }
    }

    // Añadir imágenes procesadas de esta carpeta a la lista general
    processedImages.push(...folderProcessedImages);
  }

  // Actualizar catálogo de imágenes si hay imágenes procesadas
  if (processedImages.length > 0) {
    updateImageCatalog(processedImages);
  }

  // Mostrar estadísticas finales
  console.log('\n================================================================');
  console.log('📊 RESUMEN FINAL DEL PROCESAMIENTO');
  console.log('================================================================');
  console.log(`✅ Imágenes convertidas a WebP: ${convertedCount}`);
  console.log(`🔧 Imágenes WebP optimizadas: ${optimizedCount}`);
  console.log(`⏭️ Imágenes omitidas: ${skippedCount}`);
  console.log(`❌ Errores encontrados: ${errorCount}`);
  console.log(`📁 Total de imágenes procesadas: ${processedImages.length}`);

  if (totalOriginalSize > 0) {
    console.log(`\n💾 ESTADÍSTICAS DE TAMAÑO:`);
    console.log(`   📏 Tamaño original total: ${(totalOriginalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   📏 Nuevo tamaño total: ${(totalNewSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   💰 Ahorro total: ${((totalOriginalSize - totalNewSize) / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   📊 Porcentaje de ahorro: ${((totalOriginalSize - totalNewSize) / totalOriginalSize * 100).toFixed(2)}%`);
  }

  console.log('\n🎉 Procesamiento completado exitosamente!');
  console.log('================================================================\n');
}

// Ejecutar el script con manejo robusto de errores
main().catch(error => {
  console.error('\n❌ ERROR CRÍTICO EN EL SCRIPT:');
  console.error('================================');
  console.error(`Mensaje: ${error.message}`);
  if (error.stack) {
    console.error(`Stack trace: ${error.stack}`);
  }
  console.error('================================\n');
  console.error('💡 Sugerencias para resolver el problema:');
  console.error('   1. Verificar que todas las dependencias estén instaladas (npm install)');
  console.error('   2. Comprobar que los directorios de imágenes existan');
  console.error('   3. Verificar permisos de escritura en los directorios');
  console.error('   4. Asegurarse de que las imágenes no estén siendo utilizadas por otros procesos');
  console.error('\n');
  process.exit(1);
});
