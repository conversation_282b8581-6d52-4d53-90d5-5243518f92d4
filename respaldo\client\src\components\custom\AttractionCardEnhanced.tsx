import React from 'react';
import { motion } from 'framer-motion';
import ImageManager from './ImageManager';
import imageCatalog from '../../assets/image-catalog.json';
import { openOptimizedDirections, type Coordinates } from '../../utils/navigationUtils';

interface AttractionCardEnhancedProps {
  attractionId: string;
  index: number;
}

/**
 * Componente mejorado para mostrar tarjetas de atracciones turísticas
 * Utiliza el sistema de gestión de activos para cargar imágenes y datos
 *
 * @param attractionId - ID de la atracción en el catálogo
 * @param index - Índice para la animación escalonada
 */
const AttractionCardEnhanced: React.FC<AttractionCardEnhancedProps> = ({
  attractionId,
  index
}) => {
  // Buscar la atracción en el catálogo
  const attraction = imageCatalog.attractions.find(attr => attr.id === attractionId);

  if (!attraction) {
    console.error(`No se encontró la atracción con ID: ${attractionId}`);
    return null;
  }

  // Función para manejar la navegación
  const handleNavigation = () => {
    if (attraction.coordinates) {
      openOptimizedDirections(
        attraction.coordinates as Coordinates,
        attraction.title
      );
    } else {
      console.warn(`No se encontraron coordenadas para: ${attraction.title}`);
    }
  };

  return (
    <motion.div
      className="bg-white rounded-lg shadow-xl overflow-hidden group hover:shadow-premium"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
    >
      <div className="h-64 overflow-hidden relative">
        <ImageManager
          attractionId={attractionId}
          className="w-full h-full object-cover"
        />
        {/* Overlay de distancia con z-index alto para asegurar visibilidad */}
        <div className="absolute top-0 left-0 z-30 bg-gradient-to-r from-black/70 to-transparent px-4 py-2 text-white pointer-events-none">
          <span className="text-xs font-medium tracking-wide text-shadow-dark">{attraction.distance}</span>
        </div>
      </div>

      <div className="p-6">
        <h4 className="font-serif font-bold text-woodBrown-dark text-xl mb-2">{attraction.title}</h4>

        <p className="text-sm text-stoneGray-dark mb-4">
          {attraction.description}
        </p>

        <div className="flex justify-end">
          <button
            type="button"
            onClick={handleNavigation}
            className="text-forestGreen hover:text-forestGreen-dark transition-all duration-300 flex items-center bg-forestGreen/10 hover:bg-forestGreen/20 px-4 py-2 rounded-full shadow-sm hover:shadow-md group/btn"
            title={`Obtener direcciones a ${attraction.title}`}
          >
            <span className="material-icons text-sm mr-2 transition-transform group-hover/btn:scale-110">directions</span>
            <span className="text-sm font-medium">Cómo llegar</span>
            <span className="material-icons text-sm ml-1 transition-transform group-hover/btn:translate-x-0.5">arrow_forward</span>
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default AttractionCardEnhanced;
