import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import SimpleImage from './SimpleImage';

type ModalidadType = 'modalidad-1' | 'modalidad-2';

const CabanaSection = () => {
  const [activeTab, setActiveTab] = useState<ModalidadType>('modalidad-1');

  const quickFactItems = [
    { icon: 'privacy_tip', text: '100% Privado' },
    { icon: 'photo_camera', text: 'Vistas Panorámicas' },
    { icon: 'local_fire_department', text: 'Quincho Exclusivo' },
    { icon: 'pool', text: 'Piscinas Privadas' },
  ];

  const equipmentItems = [
    {
      icon: 'live_tv',
      title: 'Televisores Premium',
      description: 'Televisores con TV cable HD en dormitorios y sala de estar.'
    },
    {
      icon: 'local_parking',
      title: 'Estacionamiento Privado',
      description: 'Amplio espacio para hasta 3 vehículos con total seguridad.'
    },
    {
      icon: 'outdoor_grill',
      title: 'Quincho Privado',
      description: 'Completamente equipado para asados y reuniones al aire libre.'
    },
    {
      icon: 'pool',
      title: 'Piscinas Exclusivas',
      description: 'Piscina para adultos y niños de uso exclusivo para los huéspedes.'
    },
    {
      icon: 'sports_volleyball',
      title: 'Cama Saltarina',
      description: 'Diversión garantizada para los más pequeños en un entorno seguro.'
    },
    {
      icon: 'sports_esports',
      title: 'Juegos Recreativos',
      description: 'Mesa de pool y TACA TACA para entretenimiento de todas las edades.'
    }
  ];

  return (
    <section id="cabana" className="py-20 bg-linen relative">
      {/* Background texture */}
      <div className="texture-background"></div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-woodBrown-dark mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5 }}
          >
            Descubre Nuestra Cabaña
          </motion.h2>
          <motion.p
            className="text-lg md:text-xl text-stoneGray-dark max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Una sola y espaciosa cabaña diseñada para adaptarse a diferentes tamaños de grupos, ofreciendo siempre total privacidad y acceso exclusivo a todas las instalaciones.
          </motion.p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8 mb-16">
          {/* Cabaña Introduction */}
          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            <div className="rounded-lg overflow-hidden shadow-xl h-96 md:h-128">
              <SimpleImage
                src="/src/assets/cabaña/living.jpg"
                alt="Interior de nuestra cabaña premium con elegantes detalles en madera"
                className="w-full h-full object-cover transition-transform duration-700 hover:scale-105"
              />
            </div>
          </motion.div>

          <motion.div
            className="lg:w-1/2 flex flex-col justify-center"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            <h3 className="text-2xl md:text-3xl font-serif font-bold text-woodBrown-dark mb-6">Flexibilidad y Confort a tu Medida</h3>

            <div className="prose prose-lg max-w-none">
              <p className="mb-6">
                Nuestra cabaña está diseñada para ofrecer una experiencia Premium, adaptándose perfectamente a las necesidades de cada grupo. Tanto para familias como para amigos, contamos con configuraciones que garantizan comodidad sin sacrificar privacidad.
              </p>

              <p className="text-forestGreen font-medium text-lg mb-6">
                Sin importar la modalidad que elijas, tendrás acceso EXCLUSIVO a toda la propiedad y sus instalaciones. La cabaña completa y sus terrenos son solo para ti durante tu estadía.
              </p>
            </div>

            <div className="mt-4 flex space-x-6">
              {quickFactItems.map((item, index) => (
                <div key={index} className="flex flex-col items-center">
                  <span className="material-icons text-amber-600 text-3xl mb-2">{item.icon}</span>
                  <span className="text-sm text-stoneGray-dark text-center">{item.text}</span>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Modalidades Tabs */}
        <motion.div
          className="bg-white rounded-lg shadow-xl p-8 mb-16"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6 }}
        >
          <h3 className="text-2xl md:text-3xl font-serif font-bold text-woodBrown-dark mb-6 text-center">Opciones de Configuración</h3>

          <Tabs defaultValue="modalidad-1" onValueChange={(value) => setActiveTab(value as ModalidadType)} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="modalidad-1">
                Opción Familiar: 2 Dormitorios
              </TabsTrigger>
              <TabsTrigger value="modalidad-2">
                Opción Grupo Extendido: 3 Dormitorios
              </TabsTrigger>
            </TabsList>

            {/* Tab Content */}
            <TabsContent value="modalidad-1" className="mt-8">
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="lg:w-1/2">
                  <div className="rounded-lg overflow-hidden shadow-lg h-80">
                    <SimpleImage
                      src="/src/assets/cabaña/piezas/pieza1.jpg"
                      alt="Dormitorio principal con cama king size y acabados premium"
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="rounded-lg overflow-hidden shadow-lg h-48">
                      <SimpleImage
                        src="/src/assets/cabaña/piezas/pieza2.jpg"
                        alt="Segundo dormitorio con camas gemelas"
                        className="w-full h-full object-cover"
                      />
                    </div>

                    <div className="rounded-lg overflow-hidden shadow-lg h-48">
                      <SimpleImage
                        src="/src/assets/cabaña/baño.jpg"
                        alt="Baño completo con acabados premium"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                </div>

                <div className="lg:w-1/2">
                  <h4 className="text-xl font-serif font-bold text-woodBrown-dark mb-4">Opción Familiar: Hasta 6 Huéspedes</h4>

                  <p className="mb-6 text-stoneGray-dark">
                    Perfecta para familias o grupos pequeños, esta configuración ofrece 2 dormitorios totalmente equipados, manteniendo todas las comodidades y el acceso exclusivo a las instalaciones completas.
                  </p>

                  <div className="mb-6">
                    <div className="flex items-center mb-2">
                      <span className="material-icons text-forestGreen mr-2">people</span>
                      <span className="font-medium">Capacidad máxima: 6 personas</span>
                    </div>

                    <div className="flex items-center mb-2">
                      <span className="material-icons text-forestGreen mr-2">king_bed</span>
                      <span className="font-medium">2 Dormitorios</span>
                    </div>

                    <div className="flex items-center">
                      <span className="material-icons text-forestGreen mr-2">payments</span>
                      <span className="font-medium">Precio: $90.000 CLP / Noche</span>
                    </div>
                  </div>

                  <a
                    href="#reservar"
                    onClick={(e) => {
                      e.preventDefault();
                      document.getElementById('reservar')?.scrollIntoView({ behavior: 'smooth' });
                    }}
                    className="inline-block px-6 py-3 bg-woodBrown hover:bg-woodBrown-dark text-white rounded-sm shadow transition-all transform hover:-translate-y-0.5 font-medium"
                  >
                    Consultar Disponibilidad
                  </a>
                </div>
              </div>
            </TabsContent>

            {/* Modalidad 2 */}
            <TabsContent value="modalidad-2" className="mt-8">
              <div className="flex flex-col lg:flex-row gap-8">
                <div className="lg:w-1/2">
                  <div className="rounded-lg overflow-hidden shadow-lg h-80">
                    <SimpleImage
                      src="/src/assets/cabaña/mesa-pool.jpg"
                      alt="Amplia sala de estar para grupos grandes"
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="rounded-lg overflow-hidden shadow-lg h-48">
                      <SimpleImage
                        src="/src/assets/cabaña/piezas/pieza3.jpg"
                        alt="Tercer dormitorio para huéspedes adicionales"
                        className="w-full h-full object-cover"
                      />
                    </div>

                    <div className="rounded-lg overflow-hidden shadow-lg h-48">
                      <SimpleImage
                        src="/src/assets/cabaña/cocina.jpg"
                        alt="Espacioso comedor para grupos grandes"
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                </div>

                <div className="lg:w-1/2">
                  <h4 className="text-xl font-serif font-bold text-woodBrown-dark mb-4">Opción Grupo Extendido: Hasta 9-10 Huéspedes</h4>

                  <p className="mb-6 text-stoneGray-dark">
                    Ideal para grupos más grandes, esta configuración aprovecha al máximo los espacios, ofreciendo 3 dormitorios y todas las comodidades para hasta 10 personas, sin sacrificar el confort y la exclusividad.
                  </p>

                  <div className="mb-6">
                    <div className="flex items-center mb-2">
                      <span className="material-icons text-forestGreen mr-2">people</span>
                      <span className="font-medium">Capacidad máxima: 9-10 personas</span>
                    </div>

                    <div className="flex items-center mb-2">
                      <span className="material-icons text-forestGreen mr-2">king_bed</span>
                      <span className="font-medium">3 Dormitorios</span>
                    </div>

                    <div className="flex items-center">
                      <span className="material-icons text-forestGreen mr-2">payments</span>
                      <span className="font-medium">Precio: $120.000 CLP / Noche</span>
                    </div>
                  </div>

                  <a
                    href="#reservar"
                    onClick={(e) => {
                      e.preventDefault();
                      document.getElementById('reservar')?.scrollIntoView({ behavior: 'smooth' });
                    }}
                    className="inline-block px-6 py-3 bg-woodBrown hover:bg-woodBrown-dark text-white rounded-sm shadow transition-all transform hover:-translate-y-0.5 font-medium"
                  >
                    Consultar Disponibilidad
                  </a>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </motion.div>

        {/* Equipamiento */}
        <motion.div
          className="bg-white rounded-lg shadow-xl p-8"
          initial={{ opacity: 0, y: 40 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.2 }}
          transition={{ duration: 0.6 }}
        >
          <div className="text-center mb-10">
            <h3 className="text-2xl md:text-3xl font-serif font-bold text-woodBrown-dark mb-4">Equipamiento General</h3>
            <p className="text-stoneGray-dark max-w-3xl mx-auto">
              Todo el equipamiento está disponible de forma EXCLUSIVA para los huéspedes, independientemente de la configuración elegida.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {equipmentItems.map((item, index) => (
              <motion.div
                key={index}
                className="flex items-start p-6 bg-linen bg-opacity-50 rounded-lg hover:shadow-lg transition-shadow"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <span className="material-icons text-amber-600 text-3xl mr-4">{item.icon}</span>
                <div>
                  <h4 className="font-medium text-woodBrown-dark mb-2">{item.title}</h4>
                  <p className="text-sm text-stoneGray-dark">{item.description}</p>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="mt-10 p-6 bg-amber-500 bg-opacity-10 border-l-4 border-amber-600 rounded-r-lg">
            <h4 className="font-serif font-bold text-woodBrown-dark mb-2">IMPORTANTE:</h4>
            <p className="text-stoneGray-dark">
              Al arrendar nuestra cabaña, independientemente de la configuración de dormitorios que elija, usted y su grupo tendrán <strong>ACCESO EXCLUSIVO Y PRIVADO</strong> a TODAS las instalaciones mencionadas (quincho, piscinas, estacionamiento, áreas de juego, etc.). La cabaña completa y sus terrenos son solo para ustedes durante su estadía, garantizando total privacidad.
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default CabanaSection;
