# 🔧 Comandos Esenciales - La Posada del Oso

## 📋 Información General
Este documento contiene todos los comandos esenciales para la gestión, mantenimiento y monitoreo del proyecto Posada 2.0.

---

## 🗄️ GESTIÓN DE BASE DE DATOS POSTGRESQL

### **Respaldos de Base de Datos**

#### Respaldo Manual Básico
```powershell
# Respaldo básico con configuración por defecto
npm run db:backup

# Respaldo con opciones personalizadas
powershell -ExecutionPolicy Bypass -File scripts/backup-database.ps1 -BackupPath "./backups" -Compress -Encrypt -RetentionDays 30
```

#### Respaldo Programado (Configurar en Windows Task Scheduler)
```powershell
# Comando para programar respaldos semanales
schtasks /create /tn "Posada_Backup_Semanal" /tr "powershell.exe -ExecutionPolicy Bypass -File 'C:\ruta\completa\al\proyecto\scripts\backup-database.ps1'" /sc weekly /d SUN /st 02:00
```

### **Restauración de Base de Datos**

#### Restaurar desde Respaldo
```powershell
# Restauración interactiva
npm run db:restore

# Restauración directa con archivo específico
powershell -ExecutionPolicy Bypass -File scripts/restore-database.ps1 -BackupFile "./backups/posada_backup_2024-01-15_02-00-00.sql"
```

#### Verificar Integridad Post-Restauración
```powershell
# Verificar conexión y esquema
npm run db:push
npm run db:monitor
```

### **Monitoreo de Base de Datos**

#### Monitoreo Básico
```powershell
# Estado actual de la base de datos
npm run db:monitor

# Monitoreo detallado con estadísticas avanzadas
npm run db:monitor-detailed

# Monitoreo continuo (cada 60 segundos)
npm run db:monitor-continuous
```

#### Comandos Directos de Monitoreo
```powershell
# Monitoreo personalizado
powershell -ExecutionPolicy Bypass -File scripts/monitor-database.ps1 -Detailed -Continuous -IntervalSeconds 30
```

---

## 📊 LOGS Y MONITOREO DEL SISTEMA

### **Visualización de Logs**

#### Logs de Aplicación
```powershell
# Ver logs generales (últimas 50 líneas)
Get-Content logs/database.log -Tail 50

# Ver logs de errores críticos
Get-Content logs/database-error.log -Tail 20

# Seguimiento en tiempo real de logs
Get-Content logs/database.log -Wait -Tail 10
```

#### Logs de Seguridad
```powershell
# Ver logs de seguridad
Get-Content logs/security.log -Tail 50

# Ver errores de seguridad críticos
Get-Content logs/security-error.log -Tail 20

# Buscar eventos específicos de seguridad
Select-String -Path logs/security.log -Pattern "CRITICAL|ERROR|ATTACK"
```

### **Análisis de Logs**

#### Filtrar Logs por Fecha
```powershell
# Logs de hoy
Get-Content logs/database.log | Select-String (Get-Date -Format "yyyy-MM-dd")

# Logs de la última semana
$fechaInicio = (Get-Date).AddDays(-7).ToString("yyyy-MM-dd")
Get-Content logs/database.log | Select-String $fechaInicio
```

#### Estadísticas de Logs
```powershell
# Contar errores por tipo
Get-Content logs/database-error.log | Group-Object | Sort-Object Count -Descending

# Buscar patrones de ataques
Select-String -Path logs/security.log -Pattern "rate.limit|csrf|suspicious" | Group-Object Line
```

---

## 🔒 SEGURIDAD Y AUDITORÍAS

### **Auditorías de Seguridad**

#### Auditoría Rápida
```powershell
# Verificación básica de seguridad
powershell -ExecutionPolicy Bypass -File security-check-simple.ps1

# Auditoría simple completa
powershell -ExecutionPolicy Bypass -File security-audit-simple.ps1
```

#### Auditoría Completa
```powershell
# Auditoría detallada con reporte exportado
powershell -ExecutionPolicy Bypass -File security-audit-complete.ps1

# Verificar vulnerabilidades de dependencias
npm audit

# Auditoría con corrección automática (usar con precaución)
npm audit fix
```

### **Monitoreo de Vulnerabilidades**

#### Verificación de Dependencias
```powershell
# Ver vulnerabilidades actuales
npm audit --audit-level moderate

# Verificar solo vulnerabilidades críticas y altas
npm audit --audit-level high

# Generar reporte detallado
npm audit --json > audit-report.json
```

#### Actualización Segura de Dependencias
```powershell
# Verificar actualizaciones disponibles
npm outdated

# Actualizar dependencias menores (más seguro)
npm update

# Actualizar dependencias específicas
npm install package-name@latest
```

---

## 🔧 MANTENIMIENTO DEL SISTEMA

### **Gestión de Procesos**

#### Inicio y Parada del Servidor
```powershell
# Desarrollo
npm run dev

# Producción
npm run build
npm start

# Verificar procesos Node.js activos
Get-Process node

# Terminar procesos específicos
Stop-Process -Name node -Force
```

#### Reinicio Automatizado
```powershell
# Reiniciar servidor de desarrollo
powershell -ExecutionPolicy Bypass -File restart-server.ps1

# Reiniciar con limpieza de caché
powershell -ExecutionPolicy Bypass -File fix-vite-cache.ps1
```

### **Limpieza y Optimización**

#### Limpieza de Archivos Temporales
```powershell
# Limpiar node_modules y reinstalar
Remove-Item node_modules -Recurse -Force
npm install

# Limpiar caché de npm
npm cache clean --force

# Limpiar logs antiguos (más de 30 días)
Get-ChildItem logs/*.log | Where-Object {$_.LastWriteTime -lt (Get-Date).AddDays(-30)} | Remove-Item
```

#### Optimización de Base de Datos
```powershell
# Ejecutar VACUUM en PostgreSQL (optimización)
psql -h localhost -U usuario -d posada_db -c "VACUUM ANALYZE;"

# Verificar estadísticas de tablas
psql -h localhost -U usuario -d posada_db -c "SELECT schemaname,tablename,n_tup_ins,n_tup_upd,n_tup_del FROM pg_stat_user_tables;"
```

---

## 🚀 DESPLIEGUE Y PRODUCCIÓN

### **Preparación para Producción**

#### Verificación Pre-Despliegue
```powershell
# Compilar TypeScript
npm run check

# Construir aplicación
npm run build

# Verificar configuración de producción
powershell -ExecutionPolicy Bypass -File security-audit-complete.ps1
```

#### Configuración de Variables de Entorno
```powershell
# Copiar plantilla de producción
Copy-Item .env.production.example .env.production

# Verificar variables críticas
Get-Content .env.production | Select-String "DATABASE_URL|SESSION_SECRET|NODE_ENV"
```

### **Monitoreo de Producción**

#### Health Checks
```powershell
# Verificar estado del servidor
Invoke-WebRequest -Uri "http://localhost:5080/health" -Method GET

# Verificar HTTPS
Invoke-WebRequest -Uri "https://localhost:5443/health" -Method GET -SkipCertificateCheck
```

#### Monitoreo de Recursos
```powershell
# Uso de memoria por Node.js
Get-Process node | Select-Object ProcessName,WorkingSet,CPU

# Espacio en disco
Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID,Size,FreeSpace
```

---

## 📈 ANÁLISIS Y REPORTES

### **Reportes de Uso**

#### Estadísticas de Base de Datos
```powershell
# Generar reporte de uso de BD
powershell -ExecutionPolicy Bypass -File scripts/monitor-database.ps1 -Detailed > reporte-bd-$(Get-Date -Format 'yyyy-MM-dd').txt
```

#### Análisis de Logs de Acceso
```powershell
# Contar accesos por IP
Select-String -Path logs/security.log -Pattern "access" | ForEach-Object { ($_ -split " ")[3] } | Group-Object | Sort-Object Count -Descending

# Páginas más visitadas
Select-String -Path logs/security.log -Pattern "GET|POST" | ForEach-Object { ($_ -split " ")[6] } | Group-Object | Sort-Object Count -Descending
```

### **Reportes de Seguridad**

#### Resumen Semanal de Seguridad
```powershell
# Generar reporte semanal
$fecha = Get-Date -Format "yyyy-MM-dd"
powershell -ExecutionPolicy Bypass -File security-audit-complete.ps1 > "reportes/seguridad-semanal-$fecha.txt"
```

#### Análisis de Intentos de Ataque
```powershell
# Buscar intentos de ataques
Select-String -Path logs/security.log -Pattern "ATTACK|SUSPICIOUS|BLOCKED" | Group-Object | Sort-Object Count -Descending

# Análisis de rate limiting
Select-String -Path logs/security.log -Pattern "rate.limit" | Measure-Object
```

---

## 🔄 AUTOMATIZACIÓN

### **Scripts de Mantenimiento Automático**

#### Programar Tareas Recurrentes
```powershell
# Respaldo semanal (Domingos a las 2:00 AM)
schtasks /create /tn "Posada_Backup" /tr "npm run db:backup" /sc weekly /d SUN /st 02:00

# Auditoría de seguridad diaria (todos los días a las 6:00 AM)
schtasks /create /tn "Posada_Security_Audit" /tr "powershell -ExecutionPolicy Bypass -File security-audit-simple.ps1" /sc daily /st 06:00

# Limpieza de logs mensual (primer día del mes a las 3:00 AM)
schtasks /create /tn "Posada_Log_Cleanup" /tr "powershell -ExecutionPolicy Bypass -File scripts/cleanup-logs.ps1" /sc monthly /d 1 /st 03:00
```

#### Verificar Tareas Programadas
```powershell
# Listar tareas relacionadas con Posada
schtasks /query | Select-String "Posada"

# Ver detalles de una tarea específica
schtasks /query /tn "Posada_Backup" /fo LIST /v
```

---

## 🆘 COMANDOS DE EMERGENCIA

### **Recuperación de Desastres**

#### Restauración Rápida
```powershell
# Parar servidor
Stop-Process -Name node -Force

# Restaurar último respaldo
$ultimoBackup = Get-ChildItem backups/posada_backup_*.sql | Sort-Object LastWriteTime -Descending | Select-Object -First 1
powershell -ExecutionPolicy Bypass -File scripts/restore-database.ps1 -BackupFile $ultimoBackup.FullName

# Reiniciar servidor
npm start
```

#### Verificación de Integridad
```powershell
# Verificar integridad de la base de datos
psql -h localhost -U usuario -d posada_db -c "SELECT pg_database_size('posada_db');"

# Verificar todas las tablas
npm run db:monitor-detailed
```

### **Resolución de Problemas Comunes**

#### Problemas de Conexión a BD
```powershell
# Verificar servicio PostgreSQL
Get-Service postgresql*

# Reiniciar PostgreSQL (si está instalado como servicio)
Restart-Service postgresql-x64-13

# Verificar conectividad
Test-NetConnection localhost -Port 5432
```

#### Problemas de Memoria
```powershell
# Limpiar caché de Node.js
npm cache clean --force

# Reiniciar con límite de memoria
node --max-old-space-size=4096 dist/index.js
```

---

## 📞 CONTACTOS Y RECURSOS

### **Comandos de Información del Sistema**
```powershell
# Versión de Node.js
node --version

# Versión de npm
npm --version

# Información del sistema
Get-ComputerInfo | Select-Object WindowsProductName,TotalPhysicalMemory

# Verificar PostgreSQL
psql --version
```

### **Logs de Ayuda**
```powershell
# Ver ayuda de scripts personalizados
Get-Help scripts/backup-database.ps1 -Full
Get-Help scripts/monitor-database.ps1 -Full
Get-Help security-audit-simple.ps1 -Full
```

---

## 🔄 CONFIGURACIÓN INICIAL DE POSTGRESQL

### **Cambiar de Memoria a PostgreSQL**

#### Paso 1: Configurar Variables de Entorno
```powershell
# Editar archivo .env
notepad .env

# Cambiar DATABASE_URL de:
# DATABASE_URL=memory://localhost
# A:
# DATABASE_URL=postgresql://usuario:contraseña@localhost:5432/posada_db
```

#### Paso 2: Verificar Conexión
```powershell
# Probar conexión a PostgreSQL
npm run db:monitor

# Si falla, configurar PostgreSQL
powershell -ExecutionPolicy Bypass -File setup-postgres.ps1
```

#### Paso 3: Migrar Esquema
```powershell
# Crear tablas en PostgreSQL
npm run db:push

# Verificar que las tablas se crearon
npm run db:monitor-detailed
```

### **Configuración de Respaldos Automáticos**

#### Crear Directorio de Respaldos
```powershell
# Crear carpeta para respaldos
New-Item -Path "backups" -ItemType Directory -Force

# Configurar permisos (solo el usuario actual)
icacls backups /inheritance:r /grant:r "$env:USERNAME:(OI)(CI)F"
```

#### Programar Respaldo Semanal
```powershell
# Programar respaldo automático cada domingo a las 2:00 AM
$action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -File '$PWD\scripts\backup-database.ps1'"
$trigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At 2:00AM
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
Register-ScheduledTask -TaskName "Posada_Backup_Semanal" -Action $action -Trigger $trigger -Settings $settings -Description "Respaldo semanal automático de la base de datos de La Posada del Oso"
```

---

## 📋 CHECKLIST DE VERIFICACIÓN DIARIA

### **Verificaciones Matutinas (5 minutos)**
```powershell
# 1. Verificar estado del servidor
Get-Process node -ErrorAction SilentlyContinue

# 2. Verificar conexión a base de datos
npm run db:monitor

# 3. Revisar logs de errores de las últimas 24 horas
Get-Content logs/database-error.log -Tail 10
Get-Content logs/security-error.log -Tail 10

# 4. Verificar espacio en disco
Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.FreeSpace -lt 1GB} | Select-Object DeviceID,FreeSpace
```

### **Verificaciones Semanales (15 minutos)**
```powershell
# 1. Auditoría de seguridad completa
powershell -ExecutionPolicy Bypass -File security-audit-simple.ps1

# 2. Verificar respaldos recientes
Get-ChildItem backups/posada_backup_*.sql | Sort-Object LastWriteTime -Descending | Select-Object -First 5

# 3. Revisar vulnerabilidades de dependencias
npm audit --audit-level moderate

# 4. Verificar tareas programadas
schtasks /query | Select-String "Posada"
```

---

**📝 Nota**: Todos los comandos deben ejecutarse desde el directorio raíz del proyecto. Para comandos que requieren PostgreSQL, asegúrate de que el servicio esté ejecutándose y las credenciales estén configuradas correctamente en el archivo `.env`.

**🔒 Seguridad**: Nunca ejecutes comandos de restauración o modificación de base de datos en producción sin antes crear un respaldo completo.

**⚡ Automatización**: Se recomienda configurar las tareas programadas para automatizar respaldos, auditorías y limpieza de logs.
