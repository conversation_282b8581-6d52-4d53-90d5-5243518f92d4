@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 20 14.3% 4.1%;
    --muted: 60 4.8% 95.9%;
    --muted-foreground: 25 5.3% 44.7%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 14.3% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 20 14.3% 4.1%;
    --border: 20 5.9% 90%;
    --input: 20 5.9% 90%;
    --primary: 35 92% 44%; /* amber-gold */
    --primary-foreground: 211 100% 99%;
    --secondary: 15 35% 25%; /* wood-brown */
    --secondary-foreground: 0 0% 100%;
    --accent: 140 40% 35%; /* forest-green */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 60 9.1% 97.8%;
    --ring: 35 92% 44%;
    --radius: 0.3rem;

    /* Wood Brown Colors */
    --wood-brown: 15 35% 28%;
    --wood-brown-light: 15 35% 45%;
    --wood-brown-dark: 15 35% 20%;

    /* Forest Green Colors */
    --forest-green: 140 40% 35%;
    --forest-green-light: 140 40% 45%;
    --forest-green-dark: 140 40% 25%;

    /* Amber Gold Colors */
    --amber-gold: 35 92% 55%;
    --amber-gold-light: 35 92% 65%;
    --amber-gold-dark: 35 92% 45%;

    /* Stone Gray Colors */
    --stone-gray: 210 15% 55%;
    --stone-gray-light: 210 15% 70%;
    --stone-gray-dark: 210 15% 35%;

    /* Linen Beige Colors */
    --linen: 50 40% 90%;
    --linen-light: 50 40% 97%;
    --linen-dark: 50 40% 83%;

    /* Chart colors (keeping for compatibility) */
    --chart-1: 210 15% 55%;
    --chart-2: 35 92% 55%;
    --chart-3: 140 40% 35%;
    --chart-4: 15 35% 28%;
    --chart-5: 50 40% 90%;

    /* Sidebar colors (keeping for compatibility) */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 20 14.3% 4.1%;
    --sidebar-primary: 207 90% 54%;
    --sidebar-primary-foreground: 211 100% 99%;
    --sidebar-accent: 60 4.8% 95.9%;
    --sidebar-accent-foreground: 24 9.8% 10%;
    --sidebar-border: 20 5.9% 90%;
    --sidebar-ring: 20 14.3% 4.1%;
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 60 9.1% 97.8%;
    --muted: 12 6.5% 15.1%;
    --muted-foreground: 24 5.4% 63.9%;
    --popover: 20 14.3% 4.1%;
    --popover-foreground: 60 9.1% 97.8%;
    --card: 20 14.3% 4.1%;
    --card-foreground: 60 9.1% 97.8%;
    --border: 12 6.5% 15.1%;
    --input: 12 6.5% 15.1%;
    --primary: 35 92% 50%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 15 35% 25%;
    --secondary-foreground: 60 9.1% 97.8%;
    --accent: 140 40% 40%;
    --accent-foreground: 60 9.1% 97.8%;
    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 60 9.1% 97.8%;
    --ring: 35.5 91.7% 32.9%;
  }

  * {
    @apply border-border scroll-smooth;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans antialiased bg-linen-light text-stoneGray-dark;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }
}

@layer components {
  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-stoneGray-light bg-opacity-20;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-woodBrown bg-opacity-50 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-woodBrown bg-opacity-80;
  }

  /* Background texture */
  .bg-grain {
    background-image: url("data:image/png;base64,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");
  }

  /* Star animation for preloader */
  @keyframes twinkle {
    0% { opacity: 0.3; }
    100% { opacity: 1; }
  }

  /* Text glow effect for preloader */
  @keyframes glow {
    0% { text-shadow: 0 0 5px rgba(255, 193, 7, 0.3), 0 0 10px rgba(255, 193, 7, 0.2), 0 2px 4px rgba(0, 0, 0, 0.8); }
    50% { text-shadow: 0 0 20px rgba(255, 193, 7, 0.6), 0 0 30px rgba(255, 193, 7, 0.4), 0 2px 4px rgba(0, 0, 0, 0.8); }
    100% { text-shadow: 0 0 5px rgba(255, 193, 7, 0.3), 0 0 10px rgba(255, 193, 7, 0.2), 0 2px 4px rgba(0, 0, 0, 0.8); }
  }

  /* Text shadow for better readability */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
  }

  /* Dark text shadow for white text on varying backgrounds */
  .text-shadow-dark {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8), 0 4px 8px rgba(0, 0, 0, 0.5);
  }

  .text-effect h1 span {
    animation: glow 3s ease-in-out infinite;
  }

  .text-effect.gold h1 span {
    animation: gold-glow 3s ease-in-out infinite;
  }

  @keyframes gold-glow {
    0% { text-shadow: 0 0 5px rgba(245, 158, 11, 0.5), 0 0 10px rgba(245, 158, 11, 0.3), 0 2px 4px rgba(0, 0, 0, 0.8); }
    50% { text-shadow: 0 0 20px rgba(245, 158, 11, 0.8), 0 0 30px rgba(245, 158, 11, 0.6), 0 0 40px rgba(245, 158, 11, 0.4), 0 2px 4px rgba(0, 0, 0, 0.8); }
    100% { text-shadow: 0 0 5px rgba(245, 158, 11, 0.5), 0 0 10px rgba(245, 158, 11, 0.3), 0 2px 4px rgba(0, 0, 0, 0.8); }
  }

  /* Box shadow glow */
  .shadow-glow {
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.6), 0 0 20px rgba(245, 158, 11, 0.4);
    animation: pulse-glow 2s infinite alternate;
  }

  @keyframes pulse-glow {
    from { box-shadow: 0 0 10px rgba(245, 158, 11, 0.6), 0 0 20px rgba(245, 158, 11, 0.4); }
    to { box-shadow: 0 0 15px rgba(245, 158, 11, 0.8), 0 0 30px rgba(245, 158, 11, 0.6); }
  }

  /* Custom shadow for premium elements */
  .shadow-premium {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.05);
  }

  /* Estilos para el carrusel de imágenes */
  .carousel-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .fade-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .fade-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 1000ms cubic-bezier(0.25, 0.1, 0.25, 1);
  }

  .fade-image-current {
    z-index: 1;
  }

  .fade-image-next {
    z-index: 2;
  }

  /* Opacidades para transiciones suaves */
  .opacity-0 { opacity: 0; }
  .opacity-01 { opacity: 0.1; }
  .opacity-03 { opacity: 0.3; }
  .opacity-05 { opacity: 0.5; }
  .opacity-07 { opacity: 0.7; }
  .opacity-09 { opacity: 0.9; }
  .opacity-1 { opacity: 1; }

  /* Indicadores del carrusel (ocultos según requisito) */
  .carousel-indicators {
    display: none; /* Ocultar indicadores según requisito */
  }

  .carousel-indicator {
    display: none; /* Ocultar indicadores individuales según requisito */
  }

  /* Controles del carrusel (ocultos según requisito) */
  .carousel-controls {
    display: none; /* Ocultar controles de navegación según requisito */
  }

  .carousel-control {
    display: none; /* Ocultar botones de control según requisito */
  }
}
