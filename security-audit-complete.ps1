# =====================================================
# AUDITORÍA COMPLETA DE SEGURIDAD - LA POSADA DEL OSO
# =====================================================
# Script de auditoría de seguridad mejorado con verificaciones detalladas
# Autor: Auditoría de Seguridad Automatizada
# Fecha: Diciembre 2024

param(
    [switch]$Detailed,
    [switch]$ExportReport,
    [string]$OutputPath = "security-audit-report.txt"
)

# Colores para output
function Write-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }
function Write-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Write-Critical { param($Message) Write-Host "🚨 $Message" -ForegroundColor Red -BackgroundColor Yellow }

# Variables globales para el reporte
$Global:AuditResults = @()
$Global:SecurityScore = 0
$Global:MaxScore = 0
$Global:CriticalIssues = @()
$Global:HighIssues = @()
$Global:MediumIssues = @()

function Add-AuditResult {
    param($Category, $Test, $Status, $Score, $MaxScore, $Details = "", $Severity = "Info")

    $Global:AuditResults += [PSCustomObject]@{
        Category = $Category
        Test = $Test
        Status = $Status
        Score = $Score
        MaxScore = $MaxScore
        Details = $Details
        Severity = $Severity
        Timestamp = Get-Date
    }

    $Global:SecurityScore += $Score
    $Global:MaxScore += $MaxScore

    if ($Severity -eq "Critical") { $Global:CriticalIssues += $Test }
    elseif ($Severity -eq "High") { $Global:HighIssues += $Test }
    elseif ($Severity -eq "Medium") { $Global:MediumIssues += $Test }
}

Write-Host ""
Write-Host "🔒 AUDITORÍA COMPLETA DE SEGURIDAD - LA POSADA DEL OSO" -ForegroundColor White -BackgroundColor DarkBlue
Write-Host "================================================================" -ForegroundColor Blue
Write-Host ""

# =====================================================
# 1. ANÁLISIS DE VULNERABILIDADES DE DEPENDENCIAS
# =====================================================
Write-Host "🔍 1. ANÁLISIS DE VULNERABILIDADES DE DEPENDENCIAS" -ForegroundColor Yellow
Write-Host "---------------------------------------------------"

try {
    $auditOutput = npm audit --json 2>$null | ConvertFrom-Json
    $totalVulns = $auditOutput.metadata.vulnerabilities.total
    $critical = $auditOutput.metadata.vulnerabilities.critical
    $high = $auditOutput.metadata.vulnerabilities.high
    $moderate = $auditOutput.metadata.vulnerabilities.moderate
    $low = $auditOutput.metadata.vulnerabilities.low

    Write-Info "Total de vulnerabilidades: $totalVulns"
    Write-Info "Críticas: $critical | Altas: $high | Moderadas: $moderate | Bajas: $low"

    if ($critical -gt 0) {
        Write-Critical "CRÍTICO: $critical vulnerabilidades críticas encontradas"
        Add-AuditResult "Dependencias" "Vulnerabilidades Críticas" "FALLO" 0 10 "$critical vulnerabilidades críticas" "Critical"
    } elseif ($high -gt 0) {
        Write-Warning "ALTO: $high vulnerabilidades altas encontradas"
        Add-AuditResult "Dependencias" "Vulnerabilidades Altas" "ADVERTENCIA" 5 10 "$high vulnerabilidades altas" "High"
    } elseif ($moderate -gt 0) {
        Write-Warning "MODERADO: $moderate vulnerabilidades moderadas encontradas"
        Add-AuditResult "Dependencias" "Vulnerabilidades Moderadas" "ADVERTENCIA" 8 10 "$moderate vulnerabilidades moderadas" "Medium"
    } else {
        Write-Success "Sin vulnerabilidades críticas o altas"
        Add-AuditResult "Dependencias" "Estado de Vulnerabilidades" "ÉXITO" 10 10 "Sin vulnerabilidades críticas" "Info"
    }
} catch {
    Write-Error "Error al ejecutar npm audit"
    Add-AuditResult "Dependencias" "Análisis de Vulnerabilidades" "ERROR" 0 10 "No se pudo ejecutar npm audit" "Critical"
}

# =====================================================
# 2. VERIFICACIÓN DE CONFIGURACIÓN DE SEGURIDAD
# =====================================================
Write-Host ""
Write-Host "🛡️  2. CONFIGURACIÓN DE SEGURIDAD" -ForegroundColor Yellow
Write-Host "-----------------------------------"

# Verificar archivos de configuración
$configFiles = @(
    @{File=".env.example"; Description="Plantilla de variables de entorno"; Critical=$true},
    @{File=".gitignore"; Description="Exclusiones de Git"; Critical=$true},
    @{File="seguridad.md"; Description="Documentación de seguridad"; Critical=$false},
    @{File="SECURITY.md"; Description="Referencia de seguridad"; Critical=$false}
)

foreach ($config in $configFiles) {
    if (Test-Path $config.File) {
        Write-Success "$($config.Description) presente"
        Add-AuditResult "Configuración" $config.Description "ÉXITO" 2 2 "Archivo presente" "Info"
    } else {
        if ($config.Critical) {
            Write-Critical "$($config.Description) FALTANTE (CRÍTICO)"
            Add-AuditResult "Configuración" $config.Description "FALLO" 0 2 "Archivo crítico faltante" "Critical"
        } else {
            Write-Warning "$($config.Description) faltante"
            Add-AuditResult "Configuración" $config.Description "ADVERTENCIA" 1 2 "Archivo recomendado faltante" "Medium"
        }
    }
}

# Verificar .gitignore específico
if (Test-Path ".gitignore") {
    $gitignoreContent = Get-Content ".gitignore" -Raw
    $securityExclusions = @(".env", "certs/", "*.pem", "*.key", "*.log")

    foreach ($exclusion in $securityExclusions) {
        if ($gitignoreContent -match [regex]::Escape($exclusion)) {
            Write-Success "Exclusión de seguridad '$exclusion' configurada"
            Add-AuditResult "Configuración" "Exclusión $exclusion" "ÉXITO" 1 1 "Configurado en .gitignore" "Info"
        } else {
            Write-Warning "Exclusión de seguridad '$exclusion' faltante"
            Add-AuditResult "Configuración" "Exclusión $exclusion" "ADVERTENCIA" 0 1 "No configurado en .gitignore" "Medium"
        }
    }
}

# =====================================================
# 3. VERIFICACIÓN DE CERTIFICADOS SSL
# =====================================================
Write-Host ""
Write-Host "🔐 3. CERTIFICADOS SSL/TLS" -ForegroundColor Yellow
Write-Host "---------------------------"

$sslFiles = @("certs/cert.pem", "certs/key.pem")
foreach ($sslFile in $sslFiles) {
    if (Test-Path $sslFile) {
        Write-Success "Certificado SSL presente: $sslFile"
        Add-AuditResult "SSL/TLS" "Certificado $sslFile" "ÉXITO" 2 2 "Archivo presente" "Info"
    } else {
        Write-Critical "Certificado SSL faltante: $sslFile"
        Add-AuditResult "SSL/TLS" "Certificado $sslFile" "FALLO" 0 2 "Archivo crítico faltante" "Critical"
    }
}

# =====================================================
# 4. ANÁLISIS DE CÓDIGO DE SEGURIDAD
# =====================================================
Write-Host ""
Write-Host "🔍 4. ANÁLISIS DE CÓDIGO DE SEGURIDAD" -ForegroundColor Yellow
Write-Host "--------------------------------------"

# Verificar implementaciones de seguridad en el código
$securityChecks = @(
    @{File="server/index.ts"; Pattern="helmet\("; Description="Helmet.js implementado"},
    @{File="server/index.ts"; Pattern="rateLimit\("; Description="Rate limiting configurado"},
    @{File="server/index.ts"; Pattern="cors\("; Description="CORS configurado"},
    @{File="server/routes.ts"; Pattern="express-validator"; Description="Validación de entrada"},
    @{File="server/auth.ts"; Pattern="bcrypt"; Description="Hash seguro de contraseñas"},
    @{File="server/index.ts"; Pattern="session\("; Description="Gestión de sesiones"}
)

foreach ($check in $securityChecks) {
    if (Test-Path $check.File) {
        $content = Get-Content $check.File -Raw
        if ($content -match $check.Pattern) {
            Write-Success $check.Description
            Add-AuditResult "Código" $check.Description "ÉXITO" 3 3 "Implementado correctamente" "Info"
        } else {
            Write-Warning "$($check.Description) no encontrado"
            Add-AuditResult "Código" $check.Description "ADVERTENCIA" 1 3 "No implementado o no detectado" "Medium"
        }
    } else {
        Write-Error "Archivo no encontrado: $($check.File)"
        Add-AuditResult "Código" $check.Description "ERROR" 0 3 "Archivo no encontrado" "High"
    }
}

# =====================================================
# 5. VERIFICACIÓN DE VALIDACIÓN DE ENTRADA
# =====================================================
Write-Host ""
Write-Host "✅ 5. VALIDACIÓN DE ENTRADA" -ForegroundColor Yellow
Write-Host "----------------------------"

if (Test-Path "server/routes.ts") {
    $routesContent = Get-Content "server/routes.ts" -Raw

    # Verificar validación en formulario de contacto
    if ($routesContent -match "contactValidators") {
        Write-Success "Validación de formulario de contacto implementada"
        Add-AuditResult "Validación" "Formulario de Contacto" "ÉXITO" 5 5 "Express-validator implementado" "Info"
    } else {
        Write-Critical "Validación de formulario de contacto faltante"
        Add-AuditResult "Validación" "Formulario de Contacto" "FALLO" 0 5 "Sin validación robusta" "Critical"
    }

    # Verificar validación en booking (CRÍTICO según auditoría)
    if ($routesContent -match "bookingValidators" -or ($routesContent -match "/api/booking" -and $routesContent -match "body\(")) {
        Write-Success "Validacion de booking implementada"
        Add-AuditResult "Validacion" "Endpoint de Booking" "EXITO" 5 5 "Validacion robusta implementada" "Info"
    } else {
        Write-Critical "CRITICO: Validacion de booking faltante o incompleta"
        Add-AuditResult "Validacion" "Endpoint de Booking" "FALLO" 0 5 "Sin validacion express-validator" "Critical"
    }
}

# =====================================================
# 6. VERIFICACIÓN DE PROTECCIÓN CSRF
# =====================================================
Write-Host ""
Write-Host "🛡️  6. PROTECCIÓN CSRF" -ForegroundColor Yellow
Write-Host "----------------------"

if (Test-Path "server/index.ts") {
    $serverContent = Get-Content "server/index.ts" -Raw
    if ($serverContent -match "csrf" -or $serverContent -match "csurf") {
        Write-Success "Protección CSRF implementada"
        Add-AuditResult "CSRF" "Protección CSRF" "ÉXITO" 5 5 "Middleware CSRF configurado" "Info"
    } else {
        Write-Critical "CRITICO: Proteccion CSRF no implementada"
        Add-AuditResult "CSRF" "Proteccion CSRF" "FALLO" 0 5 "Sin proteccion contra ataques CSRF" "Critical"
    }
}

# =====================================================
# 7. VERIFICACIÓN DE LOGGING DE SEGURIDAD
# =====================================================
Write-Host ""
Write-Host "📝 7. LOGGING DE SEGURIDAD" -ForegroundColor Yellow
Write-Host "---------------------------"

$loggingImplemented = $false
if (Test-Path "server/index.ts") {
    $serverContent = Get-Content "server/index.ts" -Raw
    if ($serverContent -match "winston" -or $serverContent -match "morgan" -or $serverContent -match "security.*log") {
        Write-Success "Sistema de logging de seguridad implementado"
        Add-AuditResult "Logging" "Logging de Seguridad" "ÉXITO" 5 5 "Sistema de logs configurado" "Info"
        $loggingImplemented = $true
    }
}

if (-not $loggingImplemented) {
    Write-Critical "CRITICO: Sistema de logging de seguridad no implementado"
    Add-AuditResult "Logging" "Logging de Seguridad" "FALLO" 0 5 "Sin registro de eventos de seguridad" "Critical"
}

# =====================================================
# 8. COMPILACIÓN Y SINTAXIS
# =====================================================
Write-Host ""
Write-Host "🔧 8. COMPILACIÓN Y SINTAXIS" -ForegroundColor Yellow
Write-Host "-----------------------------"

try {
    $compileResult = npm run check 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Compilación TypeScript exitosa"
        Add-AuditResult "Compilación" "TypeScript Check" "ÉXITO" 3 3 "Sin errores de compilación" "Info"
    } else {
        Write-Warning "Errores de compilación TypeScript detectados"
        Add-AuditResult "Compilación" "TypeScript Check" "ADVERTENCIA" 1 3 "Errores de compilación presentes" "Medium"
    }
} catch {
    Write-Error "Error al ejecutar verificación de TypeScript"
    Add-AuditResult "Compilación" "TypeScript Check" "ERROR" 0 3 "No se pudo verificar compilación" "High"
}

# =====================================================
# 9. CONFIGURACIÓN DE PRODUCCIÓN
# =====================================================
Write-Host ""
Write-Host "🚀 9. PREPARACIÓN PARA PRODUCCIÓN" -ForegroundColor Yellow
Write-Host "----------------------------------"

$productionChecks = @(
    @{File=".env.production.example"; Description="Variables de entorno de producción"; Critical=$true},
    @{File="docker-compose.yml"; Description="Configuración de contenedores"; Critical=$false},
    @{File="nginx.conf"; Description="Configuración de proxy reverso"; Critical=$false}
)

foreach ($check in $productionChecks) {
    if (Test-Path $check.File) {
        Write-Success "$($check.Description) configurado"
        Add-AuditResult "Producción" $check.Description "ÉXITO" 2 2 "Configuración presente" "Info"
    } else {
        if ($check.Critical) {
            Write-Critical "$($check.Description) faltante (CRITICO para produccion)"
            Add-AuditResult "Produccion" $check.Description "FALLO" 0 2 "Configuracion critica faltante" "Critical"
        } else {
            Write-Info "$($check.Description) no configurado (opcional)"
            Add-AuditResult "Producción" $check.Description "INFO" 1 2 "Configuración opcional" "Info"
        }
    }
}

# =====================================================
# GENERACIÓN DE REPORTE FINAL
# =====================================================
Write-Host ""
Write-Host "📊 GENERANDO REPORTE FINAL..." -ForegroundColor Cyan
Write-Host ""

# Calcular puntuación final
$finalScore = if ($Global:MaxScore -gt 0) { [math]::Round(($Global:SecurityScore / $Global:MaxScore) * 10, 1) } else { 0 }
$percentage = if ($Global:MaxScore -gt 0) { [math]::Round(($Global:SecurityScore / $Global:MaxScore) * 100, 1) } else { 0 }

# Determinar estado general
$overallStatus = switch ($finalScore) {
    {$_ -ge 9} { "EXCELENTE"; $color = "Green" }
    {$_ -ge 8} { "BUENO"; $color = "Green" }
    {$_ -ge 7} { "ACEPTABLE"; $color = "Yellow" }
    {$_ -ge 6} { "NECESITA MEJORAS"; $color = "Yellow" }
    {$_ -ge 4} { "DEFICIENTE"; $color = "Red" }
    default { "CRÍTICO"; $color = "Red" }
}

Write-Host "🎯 PUNTUACIÓN FINAL DE SEGURIDAD" -ForegroundColor White -BackgroundColor DarkBlue
Write-Host "=================================" -ForegroundColor Blue
Write-Host ""
Write-Host "Puntuacion: $finalScore/10 ($percentage%)" -ForegroundColor $color
Write-Host "Estado: $overallStatus" -ForegroundColor $color
Write-Host ""

# Resumen de problemas
Write-Host "📋 RESUMEN DE PROBLEMAS ENCONTRADOS" -ForegroundColor White -BackgroundColor DarkRed
Write-Host "====================================" -ForegroundColor Red
Write-Host ""

if ($Global:CriticalIssues.Count -gt 0) {
    Write-Host "🚨 PROBLEMAS CRÍTICOS ($($Global:CriticalIssues.Count)):" -ForegroundColor Red
    foreach ($issue in $Global:CriticalIssues) {
        Write-Host "   • $issue" -ForegroundColor Red
    }
    Write-Host ""
}

if ($Global:HighIssues.Count -gt 0) {
    Write-Host "⚠️  PROBLEMAS ALTOS ($($Global:HighIssues.Count)):" -ForegroundColor Yellow
    foreach ($issue in $Global:HighIssues) {
        Write-Host "   • $issue" -ForegroundColor Yellow
    }
    Write-Host ""
}

if ($Global:MediumIssues.Count -gt 0) {
    Write-Host "📋 PROBLEMAS MODERADOS ($($Global:MediumIssues.Count)):" -ForegroundColor Cyan
    foreach ($issue in $Global:MediumIssues) {
        Write-Host "   • $issue" -ForegroundColor Cyan
    }
    Write-Host ""
}

# Recomendaciones finales
Write-Host "💡 RECOMENDACIONES PRIORITARIAS" -ForegroundColor White -BackgroundColor DarkGreen
Write-Host "===============================" -ForegroundColor Green
Write-Host ""

if ($Global:CriticalIssues.Count -gt 0) {
    Write-Host "1. 🚨 ACCIÓN INMEDIATA REQUERIDA:" -ForegroundColor Red
    Write-Host "   • Resolver todos los problemas críticos antes del despliegue" -ForegroundColor Red
    Write-Host "   • Implementar validacion completa en endpoint de booking" -ForegroundColor Red
    Write-Host "   • Anadir proteccion CSRF a formularios" -ForegroundColor Red
    Write-Host ""
}

Write-Host "2. 📈 PRÓXIMOS PASOS RECOMENDADOS:" -ForegroundColor Yellow
Write-Host "   • Implementar logging de seguridad con Winston" -ForegroundColor Yellow
Write-Host "   • Configurar variables de entorno para producción" -ForegroundColor Yellow
Write-Host "   • Establecer monitoreo de seguridad en tiempo real" -ForegroundColor Yellow
Write-Host ""

Write-Host "3. 🔄 MANTENIMIENTO CONTINUO:" -ForegroundColor Cyan
Write-Host "   • Ejecutar auditorías de seguridad mensualmente" -ForegroundColor Cyan
Write-Host "   • Mantener dependencias actualizadas" -ForegroundColor Cyan
Write-Host "   • Revisar logs de seguridad regularmente" -ForegroundColor Cyan
Write-Host ""

# Exportar reporte si se solicita
if ($ExportReport) {
    Write-Host "📄 EXPORTANDO REPORTE..." -ForegroundColor Cyan

    $reportContent = @"
AUDITORÍA DE SEGURIDAD - LA POSADA DEL OSO
==========================================
Fecha: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
Puntuacion: $finalScore/10 ($percentage%)
Estado: $overallStatus

PROBLEMAS CRÍTICOS ($($Global:CriticalIssues.Count)):
$($Global:CriticalIssues | ForEach-Object { "• $_" } | Out-String)

PROBLEMAS ALTOS ($($Global:HighIssues.Count)):
$($Global:HighIssues | ForEach-Object { "• $_" } | Out-String)

PROBLEMAS MODERADOS ($($Global:MediumIssues.Count)):
$($Global:MediumIssues | ForEach-Object { "• $_" } | Out-String)

DETALLES COMPLETOS:
==================
$($Global:AuditResults | Format-Table -AutoSize | Out-String)
"@

    $reportContent | Out-File -FilePath $OutputPath -Encoding UTF8
    Write-Success "Reporte exportado a: $OutputPath"
}

Write-Host ""
Write-Host "✅ AUDITORÍA COMPLETADA" -ForegroundColor Green
Write-Host "Consulta 'seguridad.md' para detalles completos y proximos pasos" -ForegroundColor Cyan
Write-Host ""
