# Script simple de respaldo para PostgreSQL
# Autor: Sistema de Gestion Posada 2.0

param(
    [string]$BackupPath = "./backups",
    [switch]$Compress = $false
)

Write-Host ""
Write-Host "RESPALDO DE BASE DE DATOS" -ForegroundColor Cyan
Write-Host "=" * 30 -ForegroundColor Cyan

# Verificar que estamos en el directorio correcto
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: Este script debe ejecutarse desde el directorio raiz del proyecto" -ForegroundColor Red
    exit 1
}

# Crear directorio de respaldos si no existe
if (-not (Test-Path $BackupPath)) {
    New-Item -Path $BackupPath -ItemType Directory -Force | Out-Null
    Write-Host "Directorio de respaldos creado: $BackupPath" -ForegroundColor Green
}

# Cargar variables de entorno
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^DATABASE_URL=(.*)$") {
            $env:DATABASE_URL = $matches[1]
        }
    }
} else {
    Write-Host "ERROR: Archivo .env no encontrado" -ForegroundColor Red
    exit 1
}

$databaseUrl = $env:DATABASE_URL

if (-not $databaseUrl) {
    Write-Host "ERROR: DATABASE_URL no configurado" -ForegroundColor Red
    exit 1
}

Write-Host "DATABASE_URL: $databaseUrl"

# Verificar tipo de base de datos
if ($databaseUrl -match "memory://") {
    Write-Host "ESTADO: Base de datos en memoria detectada" -ForegroundColor Yellow
    Write-Host "INFO: No se puede respaldar base de datos en memoria" -ForegroundColor Yellow
    Write-Host "RECOMENDACION: Migrar a PostgreSQL para habilitar respaldos" -ForegroundColor Cyan
    
    # Crear archivo de estado para documentar
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
    $statusFile = Join-Path $BackupPath "estado_memoria_$timestamp.txt"
    
    $statusContent = @"
ESTADO DE BASE DE DATOS - $timestamp
=====================================

Tipo: Almacenamiento en memoria
URL: $databaseUrl
Estado: Activo pero no respaldable

NOTA: La base de datos esta configurada para usar almacenamiento en memoria.
Los datos se pierden al reiniciar la aplicacion.

Para habilitar respaldos:
1. Configurar PostgreSQL
2. Cambiar DATABASE_URL a postgresql://...
3. Ejecutar npm run db:push
4. Ejecutar npm run db:backup

Archivos del proyecto respaldados en esta fecha:
- package.json
- .env.example
- Scripts de gestion
"@
    
    $statusContent | Set-Content $statusFile
    Write-Host "Archivo de estado creado: $statusFile" -ForegroundColor Green
    
    # Crear respaldo de archivos de configuracion importantes
    $configBackupPath = Join-Path $BackupPath "config_backup_$timestamp"
    New-Item -Path $configBackupPath -ItemType Directory -Force | Out-Null
    
    # Respaldar archivos importantes
    $filesToBackup = @(
        "package.json",
        ".env.example",
        "drizzle.config.ts"
    )
    
    foreach ($file in $filesToBackup) {
        if (Test-Path $file) {
            Copy-Item $file $configBackupPath
            Write-Host "Respaldado: $file" -ForegroundColor Green
        }
    }
    
    # Respaldar scripts
    if (Test-Path "scripts") {
        Copy-Item "scripts" $configBackupPath -Recurse
        Write-Host "Respaldados: scripts/" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "RESPALDO DE CONFIGURACION COMPLETADO" -ForegroundColor Green
    Write-Host "Ubicacion: $configBackupPath" -ForegroundColor Cyan
    
} elseif ($databaseUrl -match "postgresql://") {
    Write-Host "ESTADO: PostgreSQL detectado" -ForegroundColor Green
    
    # Parsear URL de PostgreSQL
    if ($databaseUrl -match "postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)") {
        $dbUser = $matches[1]
        $dbPassword = $matches[2]
        $dbHost = $matches[3]
        $dbPort = $matches[4]
        $dbName = $matches[5]
        
        Write-Host "Usuario: $dbUser"
        Write-Host "Host: $dbHost"
        Write-Host "Puerto: $dbPort"
        Write-Host "Base de datos: $dbName"
        
        # Configurar variable de entorno para password
        $env:PGPASSWORD = $dbPassword
        
        # Crear nombre de archivo de respaldo
        $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
        $backupFile = Join-Path $BackupPath "posada_backup_$timestamp.sql"
        
        Write-Host ""
        Write-Host "Iniciando respaldo de PostgreSQL..." -ForegroundColor Cyan
        
        try {
            # Ejecutar pg_dump
            $pgDumpArgs = @(
                "-h", $dbHost,
                "-p", $dbPort,
                "-U", $dbUser,
                "-d", $dbName,
                "--no-password",
                "--verbose",
                "--clean",
                "--if-exists",
                "--create",
                "--file", $backupFile
            )
            
            & pg_dump @pgDumpArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Respaldo completado exitosamente" -ForegroundColor Green
                Write-Host "Archivo: $backupFile" -ForegroundColor Cyan
                
                # Mostrar tamaño del archivo
                $fileSize = (Get-Item $backupFile).Length
                $fileSizeMB = [math]::Round($fileSize / 1MB, 2)
                Write-Host "Tamaño: $fileSizeMB MB" -ForegroundColor Cyan
                
                # Comprimir si se solicita
                if ($Compress) {
                    Write-Host "Comprimiendo respaldo..." -ForegroundColor Cyan
                    $compressedFile = "$backupFile.zip"
                    Compress-Archive -Path $backupFile -DestinationPath $compressedFile
                    Remove-Item $backupFile
                    Write-Host "Respaldo comprimido: $compressedFile" -ForegroundColor Green
                }
                
            } else {
                Write-Host "ERROR: Fallo en el respaldo de PostgreSQL" -ForegroundColor Red
                exit 1
            }
            
        } catch {
            Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
            exit 1
        } finally {
            # Limpiar variable de entorno
            Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue
        }
        
    } else {
        Write-Host "ERROR: Formato de URL PostgreSQL invalido" -ForegroundColor Red
        exit 1
    }
    
} else {
    Write-Host "ERROR: Tipo de base de datos no reconocido" -ForegroundColor Red
    Write-Host "URL: $databaseUrl" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "RESPALDO COMPLETADO" -ForegroundColor Green
