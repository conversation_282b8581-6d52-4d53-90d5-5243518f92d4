# =====================================================
# AUDITORIA COMPLETA DE SEGURIDAD - LA POSADA DEL OSO
# =====================================================

# Colores para output
function Write-Success { param($Message) Write-Host "OK: $Message" -ForegroundColor Green }
function Write-Warning { param($Message) Write-Host "ADVERTENCIA: $Message" -ForegroundColor Yellow }
function Write-Error { param($Message) Write-Host "ERROR: $Message" -ForegroundColor Red }
function Write-Critical { param($Message) Write-Host "CRITICO: $Message" -ForegroundColor Red -BackgroundColor Yellow }

Write-Host ""
Write-Host "AUDITORIA COMPLETA DE SEGURIDAD - LA POSADA DEL OSO" -ForegroundColor White -BackgroundColor DarkBlue
Write-Host "====================================================" -ForegroundColor Blue
Write-Host ""

$criticalIssues = 0
$highIssues = 0
$mediumIssues = 0
$totalScore = 0
$maxScore = 0

# =====================================================
# 1. ANALISIS DE VULNERABILIDADES DE DEPENDENCIAS
# =====================================================
Write-Host "1. ANALISIS DE VULNERABILIDADES DE DEPENDENCIAS" -ForegroundColor Yellow
Write-Host "------------------------------------------------"

try {
    $auditOutput = npm audit --json 2>$null | ConvertFrom-Json
    $totalVulns = $auditOutput.metadata.vulnerabilities.total
    $critical = $auditOutput.metadata.vulnerabilities.critical
    $high = $auditOutput.metadata.vulnerabilities.high
    $moderate = $auditOutput.metadata.vulnerabilities.moderate

    Write-Host "Total de vulnerabilidades: $totalVulns"
    Write-Host "Criticas: $critical | Altas: $high | Moderadas: $moderate"

    if ($critical -gt 0) {
        Write-Critical "$critical vulnerabilidades criticas encontradas"
        $criticalIssues++
    } elseif ($high -gt 0) {
        Write-Warning "$high vulnerabilidades altas encontradas"
        $highIssues++
        $totalScore += 5
    } elseif ($moderate -gt 0) {
        Write-Warning "$moderate vulnerabilidades moderadas encontradas"
        $mediumIssues++
        $totalScore += 8
    } else {
        Write-Success "Sin vulnerabilidades criticas o altas"
        $totalScore += 10
    }
    $maxScore += 10
} catch {
    Write-Error "Error al ejecutar npm audit"
    $criticalIssues++
    $maxScore += 10
}

# =====================================================
# 2. VERIFICACION DE CONFIGURACION DE SEGURIDAD
# =====================================================
Write-Host ""
Write-Host "2. CONFIGURACION DE SEGURIDAD" -ForegroundColor Yellow
Write-Host "------------------------------"

$configFiles = @(".env.example", ".gitignore", "seguridad.md")
foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Success "Archivo $file presente"
        $totalScore += 2
    } else {
        Write-Warning "Archivo $file faltante"
        $mediumIssues++
    }
    $maxScore += 2
}

# Verificar .gitignore especifico
if (Test-Path ".gitignore") {
    $gitignoreContent = Get-Content ".gitignore" -Raw
    $securityExclusions = @(".env", "certs/", "*.pem", "*.key")

    foreach ($exclusion in $securityExclusions) {
        if ($gitignoreContent -match [regex]::Escape($exclusion)) {
            Write-Success "Exclusion de seguridad '$exclusion' configurada"
            $totalScore += 1
        } else {
            Write-Warning "Exclusion de seguridad '$exclusion' faltante"
            $mediumIssues++
        }
        $maxScore += 1
    }
}

# =====================================================
# 3. VERIFICACION DE CERTIFICADOS SSL
# =====================================================
Write-Host ""
Write-Host "3. CERTIFICADOS SSL/TLS" -ForegroundColor Yellow
Write-Host "------------------------"

$sslFiles = @("certs/cert.pem", "certs/key.pem")
foreach ($sslFile in $sslFiles) {
    if (Test-Path $sslFile) {
        Write-Success "Certificado SSL presente: $sslFile"
        $totalScore += 2
    } else {
        Write-Critical "Certificado SSL faltante: $sslFile"
        $criticalIssues++
    }
    $maxScore += 2
}

# =====================================================
# 4. ANALISIS DE CODIGO DE SEGURIDAD
# =====================================================
Write-Host ""
Write-Host "4. ANALISIS DE CODIGO DE SEGURIDAD" -ForegroundColor Yellow
Write-Host "-----------------------------------"

$securityChecks = @(
    @{File="server/index.ts"; Pattern="helmet\("; Description="Helmet.js implementado"},
    @{File="server/index.ts"; Pattern="rateLimit\("; Description="Rate limiting configurado"},
    @{File="server/index.ts"; Pattern="cors\("; Description="CORS configurado"},
    @{File="server/routes.ts"; Pattern="express-validator"; Description="Validacion de entrada"},
    @{File="server/auth.ts"; Pattern="bcrypt"; Description="Hash seguro de contraseñas"},
    @{File="server/index.ts"; Pattern="session\("; Description="Gestion de sesiones"}
)

foreach ($check in $securityChecks) {
    if (Test-Path $check.File) {
        $content = Get-Content $check.File -Raw
        if ($content -match $check.Pattern) {
            Write-Success $check.Description
            $totalScore += 3
        } else {
            Write-Warning "$($check.Description) no encontrado"
            $mediumIssues++
            $totalScore += 1
        }
    } else {
        Write-Error "Archivo no encontrado: $($check.File)"
        $highIssues++
    }
    $maxScore += 3
}

# =====================================================
# 5. VERIFICACION DE VALIDACION DE ENTRADA
# =====================================================
Write-Host ""
Write-Host "5. VALIDACION DE ENTRADA" -ForegroundColor Yellow
Write-Host "-------------------------"

if (Test-Path "server/routes.ts") {
    $routesContent = Get-Content "server/routes.ts" -Raw

    # Verificar validacion en formulario de contacto
    if ($routesContent -match "contactValidators") {
        Write-Success "Validacion de formulario de contacto implementada"
        $totalScore += 5
    } else {
        Write-Critical "Validacion de formulario de contacto faltante"
        $criticalIssues++
    }
    $maxScore += 5

    # Verificar validacion en booking (CRITICO segun auditoria)
    if ($routesContent -match "bookingValidators" -or ($routesContent -match "/api/booking" -and $routesContent -match "body\(")) {
        Write-Success "Validacion de booking implementada"
        $totalScore += 5
    } else {
        Write-Critical "CRITICO: Validacion de booking faltante o incompleta"
        $criticalIssues++
    }
    $maxScore += 5
}

# =====================================================
# 6. VERIFICACION DE PROTECCION CSRF
# =====================================================
Write-Host ""
Write-Host "6. PROTECCION CSRF" -ForegroundColor Yellow
Write-Host "------------------"

if (Test-Path "server/index.ts") {
    $serverContent = Get-Content "server/index.ts" -Raw
    if ($serverContent -match "csrf" -or $serverContent -match "csurf") {
        Write-Success "Proteccion CSRF implementada"
        $totalScore += 5
    } else {
        Write-Critical "CRITICO: Proteccion CSRF no implementada"
        $criticalIssues++
    }
    $maxScore += 5
}

# =====================================================
# 7. VERIFICACION DE LOGGING DE SEGURIDAD
# =====================================================
Write-Host ""
Write-Host "7. LOGGING DE SEGURIDAD" -ForegroundColor Yellow
Write-Host "-----------------------"

$loggingImplemented = $false
if (Test-Path "server/index.ts") {
    $serverContent = Get-Content "server/index.ts" -Raw
    if ($serverContent -match "winston" -or $serverContent -match "morgan" -or $serverContent -match "security.*log") {
        Write-Success "Sistema de logging de seguridad implementado"
        $totalScore += 5
        $loggingImplemented = $true
    }
}

if (-not $loggingImplemented) {
    Write-Critical "CRITICO: Sistema de logging de seguridad no implementado"
    $criticalIssues++
}
$maxScore += 5

# =====================================================
# 8. COMPILACION Y SINTAXIS
# =====================================================
Write-Host ""
Write-Host "8. COMPILACION Y SINTAXIS" -ForegroundColor Yellow
Write-Host "-------------------------"

try {
    npm run check 2>&1 | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Compilacion TypeScript exitosa"
        $totalScore += 3
    } else {
        Write-Warning "Errores de compilacion TypeScript detectados"
        $mediumIssues++
        $totalScore += 1
    }
} catch {
    Write-Error "Error al ejecutar verificacion de TypeScript"
    $highIssues++
}
$maxScore += 3

# =====================================================
# REPORTE FINAL
# =====================================================
Write-Host ""
Write-Host "REPORTE FINAL DE AUDITORIA" -ForegroundColor White -BackgroundColor DarkBlue
Write-Host "===========================" -ForegroundColor Blue
Write-Host ""

$finalScore = if ($maxScore -gt 0) { [math]::Round(($totalScore / $maxScore) * 10, 1) } else { 0 }
$percentage = if ($maxScore -gt 0) { [math]::Round(($totalScore / $maxScore) * 100, 1) } else { 0 }

$overallStatus = switch ($finalScore) {
    {$_ -ge 9} { "EXCELENTE"; $color = "Green" }
    {$_ -ge 8} { "BUENO"; $color = "Green" }
    {$_ -ge 7} { "ACEPTABLE"; $color = "Yellow" }
    {$_ -ge 6} { "NECESITA MEJORAS"; $color = "Yellow" }
    {$_ -ge 4} { "DEFICIENTE"; $color = "Red" }
    default { "CRITICO"; $color = "Red" }
}

Write-Host "Puntuacion: $finalScore/10 ($percentage%)" -ForegroundColor $color
Write-Host "Estado: $overallStatus" -ForegroundColor $color
Write-Host ""

Write-Host "RESUMEN DE PROBLEMAS:" -ForegroundColor White -BackgroundColor DarkRed
Write-Host "Problemas Criticos: $criticalIssues" -ForegroundColor Red
Write-Host "Problemas Altos: $highIssues" -ForegroundColor Yellow
Write-Host "Problemas Moderados: $mediumIssues" -ForegroundColor Cyan
Write-Host ""

if ($criticalIssues -gt 0) {
    Write-Host "ACCION INMEDIATA REQUERIDA:" -ForegroundColor Red
    Write-Host "• Resolver todos los problemas criticos antes del despliegue" -ForegroundColor Red
    Write-Host "• Implementar validacion completa en endpoint de booking" -ForegroundColor Red
    Write-Host "• Anadir proteccion CSRF a formularios" -ForegroundColor Red
    Write-Host "• Configurar logging de seguridad" -ForegroundColor Red
    Write-Host ""
}

Write-Host "PROXIMOS PASOS RECOMENDADOS:" -ForegroundColor Yellow
Write-Host "• Implementar logging de seguridad con Winston" -ForegroundColor Yellow
Write-Host "• Configurar variables de entorno para produccion" -ForegroundColor Yellow
Write-Host "• Establecer monitoreo de seguridad en tiempo real" -ForegroundColor Yellow
Write-Host ""

Write-Host "AUDITORIA COMPLETADA" -ForegroundColor Green
Write-Host "Consulta 'seguridad.md' para detalles completos y proximos pasos" -ForegroundColor Cyan
Write-Host ""
