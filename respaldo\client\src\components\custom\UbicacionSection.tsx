import React from 'react';
import { motion } from 'framer-motion';
import AttractionCardEnhanced from './AttractionCardEnhanced';

const UbicacionSection = () => {
  // IDs de las atracciones en el catálogo de imágenes
  const attractionIds = [
    "cocha-resbaladero",
    "salar-del-huasco",
    "pueblo-de-pica",
    "parque-dinosaurios",
    "reserva-pampa-del-tamarugal",
    "iglesia-san-andres"
  ];

  return (
    <section id="ubicacion" className="py-20 bg-stoneGray-dark relative">
      {/* Background texture */}
      <div
        className="absolute inset-0 opacity-10 pointer-events-none texture-background-ubicacion"
      ></div>

      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <motion.h2
            className="text-3xl md:text-4xl lg:text-5xl font-serif font-bold text-white mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6 }}
          >
            UBICACIÓN Y ACTIVIDADES
          </motion.h2>
          <motion.p
            className="text-lg text-gray-300 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Descubre la belleza natural y cultural que rodea nuestra cabaña en Pica, un oasis en el desierto más árido del mundo.
          </motion.p>
        </div>

        {/* Map Section */}
        <motion.div
          className="bg-white rounded-lg shadow-xl overflow-hidden mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.3 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex flex-col md:flex-row">
            <div className="md:w-1/2 p-8">
              <h3 className="text-2xl font-serif font-bold text-woodBrown-dark mb-4">Ubicación Privilegiada</h3>

              <p className="mb-6 text-stoneGray-dark">
                Nuestra cabaña está estratégicamente ubicada en Sector La Banda, Lote 9, Sitio 13, Pica, ofreciendo un perfecto balance entre tranquilidad y accesibilidad a las atracciones locales.
              </p>

              <div className="mb-6">
                <h4 className="font-medium text-woodBrown-dark mb-2">Cómo Llegar:</h4>

                <div className="flex items-start mb-4">
                  <span className="material-icons text-forestGreen mr-2 mt-1">directions_car</span>
                  <p className="text-sm text-stoneGray-dark">
                    Desde Iquique: Tomar la Ruta 1 hacia el sur, conectar con la Ruta 15 y seguir las indicaciones hacia Pica. Aproximadamente 1 hora y 45 minutos (117 km).
                  </p>
                </div>

                <div className="flex items-start">
                  <span className="material-icons text-forestGreen mr-2 mt-1">flight</span>
                  <p className="text-sm text-stoneGray-dark">
                    El aeropuerto más cercano es el Aeropuerto Internacional Diego Aracena (IQQ) en Iquique, a 120 km de distancia.
                  </p>
                </div>
              </div>

              <a
                href="https://maps.google.com/?q=-20.494676,-69.3265558"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-6 py-3 bg-forestGreen hover:bg-forestGreen-dark text-white rounded-sm shadow transition-all font-medium"
              >
                <span className="material-icons mr-2">map</span>
                Abrir en Google Maps
              </a>
            </div>

            <div className="md:w-1/2 h-80 md:h-auto map-container">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3648.8!2d-69.3265558!3d-20.494676!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjDCsDI5JzQwLjgiUyA2OcKwMTknMzUuNiJX!5e0!3m2!1ses!2scl!4v1628185012345!5m2!1ses!2scl"
                className="map-iframe"
                allowFullScreen={true}
                referrerPolicy="no-referrer-when-downgrade"
                title="Ubicación de la cabaña en La Banda, Pica, Chile"
              ></iframe>
            </div>
          </div>
        </motion.div>

        {/* Attractions */}
        <motion.h3
          className="text-2xl md:text-3xl font-serif font-bold text-white mb-8 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          Descubre los Alrededores
        </motion.h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {attractionIds.map((attractionId, index) => (
            <AttractionCardEnhanced
              key={attractionId}
              attractionId={attractionId}
              index={index}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default UbicacionSection;
