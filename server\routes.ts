import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { getStorage } from "./storage";
import path from "path";
import { type InsertBooking } from "@shared/schema";
import { body, validationResult } from "express-validator";
import rateLimit from "express-rate-limit";
import { authenticateUser, createUser, requireAuth, validatePassword, validateUsername } from "./auth";
import { logAuthEvent, logValidationError, logRateLimitHit, logSuspiciousActivity } from "./security-logger";
import { csrfTokenRoute } from "./csrf-protection";
import { monitorAuthFailure, monitorValidationError } from "./security-monitor";

// Rate limiting específico para formularios
const formLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 envíos por IP cada 15 minutos
  message: {
    error: 'Demasiados envíos de formulario. Intenta nuevamente en 15 minutos.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    logRateLimitHit(req, 'form_submission');
    res.status(429).json({
      error: 'Demasiados envíos de formulario. Intenta nuevamente en 15 minutos.'
    });
  },
});

// Rate limiting específico para autenticación
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 5, // máximo 5 intentos de login por IP cada 15 minutos
  message: {
    error: 'Demasiados intentos de inicio de sesión. Intenta nuevamente en 15 minutos.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    logRateLimitHit(req, 'authentication');
    res.status(429).json({
      error: 'Demasiados intentos de inicio de sesión. Intenta nuevamente en 15 minutos.'
    });
  },
});

export async function registerRoutes(app: Express): Promise<Server> {
  // API prefix for all routes
  const apiPrefix = '/api';

  // Ruta para instrucciones de certificado SSL
  app.get('/cert-help', (req, res) => {
    res.sendFile(path.resolve(process.cwd(), 'client/public/cert-instructions.html'));
  });

  // Ruta para obtener token CSRF
  app.get(`${apiPrefix}/csrf-token`, csrfTokenRoute());

  // ========================================
  // RUTAS DE AUTENTICACIÓN
  // ========================================

  // Login
  app.post(`${apiPrefix}/auth/login`, authLimiter, async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;

      // Validar campos requeridos
      if (!username || !password) {
        return res.status(400).json({
          message: 'Username y contraseña son requeridos'
        });
      }

      // Autenticar usuario
      const user = await authenticateUser(username, password);
      if (!user) {
        logAuthEvent(req, 'login_failed', { username }, 'medium');
        monitorAuthFailure(req, { username, reason: 'invalid_credentials' });
        return res.status(401).json({
          message: 'Credenciales inválidas'
        });
      }

      // Crear sesión
      req.session.userId = user.id;
      logAuthEvent(req, 'login_success', { userId: user.id, username: user.username }, 'low');

      return res.status(200).json({
        message: 'Inicio de sesión exitoso',
        user: {
          id: user.id,
          username: user.username
        }
      });
    } catch (error) {
      console.error('Error en login:', error);
      return res.status(500).json({
        message: 'Error interno del servidor'
      });
    }
  });

  // Logout
  app.post(`${apiPrefix}/auth/logout`, (req: Request, res: Response) => {
    const userId = req.session?.userId;
    const username = req.user?.username;

    req.session.destroy((err) => {
      if (err) {
        console.error('Error al cerrar sesión:', err);
        return res.status(500).json({
          message: 'Error al cerrar sesión'
        });
      }

      logAuthEvent(req, 'logout_success', { userId, username }, 'low');
      res.clearCookie('posada.sid');
      return res.status(200).json({
        message: 'Sesión cerrada exitosamente'
      });
    });
  });

  // Verificar estado de autenticación
  app.get(`${apiPrefix}/auth/me`, (req: Request, res: Response) => {
    if (req.user) {
      return res.status(200).json({
        user: {
          id: req.user.id,
          username: req.user.username
        }
      });
    } else {
      return res.status(401).json({
        message: 'No autenticado'
      });
    }
  });

  // Crear usuario (solo para desarrollo/setup inicial)
  app.post(`${apiPrefix}/auth/register`, authLimiter, async (req: Request, res: Response) => {
    try {
      const { username, password } = req.body;

      // Validar campos requeridos
      if (!username || !password) {
        return res.status(400).json({
          message: 'Username y contraseña son requeridos'
        });
      }

      // Validar username
      const usernameValidation = validateUsername(username);
      if (!usernameValidation.isValid) {
        return res.status(400).json({
          message: 'Username inválido',
          errors: usernameValidation.errors
        });
      }

      // Validar contraseña
      const passwordValidation = validatePassword(password);
      if (!passwordValidation.isValid) {
        return res.status(400).json({
          message: 'Contraseña inválida',
          errors: passwordValidation.errors
        });
      }

      // Crear usuario
      const newUser = await createUser({ username, password });
      if (!newUser) {
        return res.status(400).json({
          message: 'No se pudo crear el usuario. Es posible que ya exista.'
        });
      }

      return res.status(201).json({
        message: 'Usuario creado exitosamente',
        user: {
          id: newUser.id,
          username: newUser.username
        }
      });
    } catch (error) {
      console.error('Error en registro:', error);
      return res.status(500).json({
        message: 'Error interno del servidor'
      });
    }
  });

  // Validadores para el formulario de contacto
  const contactValidators = [
    body('name')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('El nombre debe tener entre 2 y 100 caracteres')
      .escape(),
    body('email')
      .isEmail()
      .withMessage('Debe proporcionar un email válido')
      .normalizeEmail(),
    body('phone')
      .optional()
      .isMobilePhone('any')
      .withMessage('Debe proporcionar un número de teléfono válido'),
    body('subject')
      .trim()
      .isLength({ min: 5, max: 200 })
      .withMessage('El asunto debe tener entre 5 y 200 caracteres')
      .escape(),
    body('message')
      .trim()
      .isLength({ min: 10, max: 2000 })
      .withMessage('El mensaje debe tener entre 10 y 2000 caracteres')
      .escape(),
  ];

  // Contact form submission
  app.post(`${apiPrefix}/contact`, formLimiter, contactValidators, async (req: Request, res: Response) => {
    try {
      // Verificar errores de validación
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        logValidationError(req, errors.array(), 'medium');
        monitorValidationError(req, { errors: errors.array(), endpoint: 'contact' });
        return res.status(400).json({
          message: 'Datos de entrada inválidos',
          errors: errors.array()
        });
      }

      const { name, email, phone, subject, message } = req.body;

      // Store the contact request
      const contactData = {
        name,
        email,
        phone: phone || '',
        subject,
        message,
        createdAt: new Date()
      };

      const storage = await getStorage();
      const savedContact = await storage.createContact(contactData);

      // In a real application, here you might:
      // 1. Send an email notification
      // 2. Add to a CRM system
      // 3. Create a task in a project management tool

      return res.status(200).json({
        message: 'Mensaje enviado correctamente',
        id: savedContact.id
      });
    } catch (error) {
      console.error('Error processing contact form:', error);
      return res.status(500).json({
        message: 'Error al procesar la solicitud. Por favor intente nuevamente.'
      });
    }
  });

  // Validadores para el formulario de booking
  const bookingValidators = [
    body('checkIn')
      .isISO8601()
      .withMessage('La fecha de llegada debe tener un formato válido (YYYY-MM-DD)')
      .custom((value) => {
        const checkInDate = new Date(value);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        if (checkInDate < today) {
          throw new Error('La fecha de llegada no puede ser anterior a hoy');
        }
        return true;
      }),
    body('checkOut')
      .isISO8601()
      .withMessage('La fecha de salida debe tener un formato válido (YYYY-MM-DD)')
      .custom((value, { req }) => {
        const checkOutDate = new Date(value);
        const checkInDate = new Date(req.body.checkIn);
        if (checkOutDate <= checkInDate) {
          throw new Error('La fecha de salida debe ser posterior a la fecha de llegada');
        }
        return true;
      }),
    body('configurationType')
      .trim()
      .isIn(['individual', 'pareja', 'familia'])
      .withMessage('El tipo de configuración debe ser: individual, pareja o familia')
      .escape(),
    body('adults')
      .isInt({ min: 1, max: 8 })
      .withMessage('El número de adultos debe ser entre 1 y 8')
      .toInt(),
    body('children')
      .isInt({ min: 0, max: 6 })
      .withMessage('El número de niños debe ser entre 0 y 6')
      .toInt(),
    body('fullName')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('El nombre completo debe tener entre 2 y 100 caracteres')
      .matches(/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/)
      .withMessage('El nombre solo puede contener letras y espacios')
      .escape(),
    body('email')
      .isEmail()
      .withMessage('Debe proporcionar un email válido')
      .normalizeEmail(),
    body('phone')
      .isMobilePhone('any')
      .withMessage('Debe proporcionar un número de teléfono válido'),
    body('specialRequests')
      .optional()
      .trim()
      .isLength({ max: 500 })
      .withMessage('Las solicitudes especiales no pueden exceder 500 caracteres')
      .escape(),
    body('pricePerNight')
      .isFloat({ min: 0 })
      .withMessage('El precio por noche debe ser un número válido mayor a 0')
      .toFloat(),
    body('nightsCount')
      .isInt({ min: 1, max: 30 })
      .withMessage('El número de noches debe ser entre 1 y 30')
      .toInt(),
    body('totalPrice')
      .isFloat({ min: 0 })
      .withMessage('El precio total debe ser un número válido mayor a 0')
      .toFloat()
      .custom((value, { req }) => {
        const expectedTotal = req.body.pricePerNight * req.body.nightsCount;
        if (Math.abs(value - expectedTotal) > 0.01) {
          throw new Error('El precio total no coincide con el cálculo (precio por noche × número de noches)');
        }
        return true;
      }),
  ];

  // Booking request submission
  app.post(`${apiPrefix}/booking`, formLimiter, bookingValidators, async (req: Request, res: Response) => {
    try {
      // Verificar errores de validación
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        logValidationError(req, errors.array(), 'medium');
        monitorValidationError(req, { errors: errors.array(), endpoint: 'booking' });
        return res.status(400).json({
          message: 'Datos de entrada inválidos',
          errors: errors.array()
        });
      }

      const {
        checkIn,
        checkOut,
        configurationType,
        adults,
        children,
        fullName,
        email,
        phone,
        specialRequests,
        pricePerNight,
        nightsCount,
        totalPrice
      } = req.body;

      // Los datos ya están validados y sanitizados por express-validator
      const checkInDate = new Date(checkIn);
      const checkOutDate = new Date(checkOut);

      // Helper function to format Date to YYYY-MM-DD string
      const formatDateToYYYYMMDD = (date: Date) => {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      // Store the booking
      const bookingData: InsertBooking = {
        checkIn: formatDateToYYYYMMDD(checkInDate),
        checkOut: formatDateToYYYYMMDD(checkOutDate),
        configurationType: String(configurationType),
        guests: {
          adults: Number(adults),
          children: Number(children),
        },
        contactInfo: {
          fullName: String(fullName),
          email: String(email),
          phone: String(phone),
        },
        specialRequests: specialRequests ? String(specialRequests) : '',
        pricing: {
          pricePerNight: Number(pricePerNight),
          nightsCount: Number(nightsCount),
          totalPrice: Number(totalPrice),
        },
        status: 'pending'
      };

      const storage = await getStorage();
      const savedBooking = await storage.createBooking(bookingData);

      // In a real application, here you might:
      // 1. Check actual availability
      // 2. Send confirmation emails
      // 3. Process payment or create payment link

      return res.status(200).json({
        message: 'Solicitud de reserva recibida correctamente',
        id: savedBooking.id,
        status: savedBooking.status
      });
    } catch (error) {
      console.error('Error processing booking request:', error);
      return res.status(500).json({
        message: 'Error al procesar la reserva. Por favor intente nuevamente.'
      });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
