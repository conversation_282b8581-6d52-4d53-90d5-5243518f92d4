# Variables de entorno para desarrollo
# NO VERSIONAR ESTE ARCHIVO EN PRODUCCIÓN

# Base de datos (usando almacenamiento en memoria para desarrollo)
DATABASE_URL=memory://localhost

# Configuración del servidor
NODE_ENV=development
PORT=5080
HTTPS_PORT=5443

# Configuración de sesiones (cambiar en producción)
SESSION_SECRET=desarrollo_clave_secreta_temporal_no_usar_en_produccion

# Configuración SSL
SSL_KEY_PATH=./certs/key.pem
SSL_CERT_PATH=./certs/cert.pem

# Configuración de CORS
ALLOWED_ORIGINS=http://localhost:5080,https://localhost:5443

# Configuración de rate limiting (más permisivo en desarrollo)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Configuración de logging
LOG_LEVEL=debug
