# Script para configurar respaldos automáticos de PostgreSQL
# Autor: Sistema de Gestión Posada 2.0
# Descripción: Configura respaldos automáticos semanales y limpieza de logs

param(
    [string]$BackupSchedule = "weekly",
    [string]$BackupTime = "02:00",
    [string]$BackupDay = "Sunday",
    [int]$RetentionDays = 30,
    [switch]$EnableLogCleanup = $true
)

# Funciones de utilidad
function Show-Info { param($Message) Write-Host "ℹ️  $Message" -ForegroundColor Cyan }
function Show-Success { param($Message) Write-Host "✅ $Message" -ForegroundColor Green }
function Show-Warning { param($Message) Write-Host "⚠️  $Message" -ForegroundColor Yellow }
function Show-Error { param($Message) Write-Host "❌ $Message" -ForegroundColor Red }

Write-Host ""
Write-Host "🔄 CONFIGURACIÓN DE RESPALDOS AUTOMÁTICOS" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Verificar que estamos en el directorio correcto
if (-not (Test-Path "package.json")) {
    Show-Error "Este script debe ejecutarse desde el directorio raíz del proyecto"
    exit 1
}

# Crear directorio de respaldos si no existe
$backupPath = Join-Path $PWD "backups"
if (-not (Test-Path $backupPath)) {
    New-Item -Path $backupPath -ItemType Directory -Force | Out-Null
    Show-Info "Directorio de respaldos creado: $backupPath"
}

# Configurar permisos del directorio de respaldos
try {
    icacls $backupPath /inheritance:r /grant:r "$env:USERNAME:(OI)(CI)F" | Out-Null
    Show-Success "Permisos de seguridad configurados para el directorio de respaldos"
} catch {
    Show-Warning "No se pudieron configurar permisos específicos: $($_.Exception.Message)"
}

# Crear directorio de reportes si no existe
$reportPath = Join-Path $PWD "reportes"
if (-not (Test-Path $reportPath)) {
    New-Item -Path $reportPath -ItemType Directory -Force | Out-Null
    Show-Info "Directorio de reportes creado: $reportPath"
}

Show-Info "Configurando tareas programadas..."

# 1. Configurar respaldo automático de base de datos
$backupScriptPath = Join-Path $PWD "scripts\backup-database.ps1"
$backupTaskName = "Posada_Backup_Automatico"

# Eliminar tarea existente si existe
try {
    Unregister-ScheduledTask -TaskName $backupTaskName -Confirm:$false -ErrorAction SilentlyContinue
} catch {
    # Ignorar si la tarea no existe
}

# Crear nueva tarea de respaldo
$backupAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -File `"$backupScriptPath`" -BackupPath `"$backupPath`" -Compress -RetentionDays $RetentionDays"

if ($BackupSchedule -eq "weekly") {
    $backupTrigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek $BackupDay -At $BackupTime
} elseif ($BackupSchedule -eq "daily") {
    $backupTrigger = New-ScheduledTaskTrigger -Daily -At $BackupTime
} else {
    Show-Error "Programación de respaldo no válida: $BackupSchedule"
    exit 1
}

$backupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
$backupPrincipal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive

try {
    Register-ScheduledTask -TaskName $backupTaskName -Action $backupAction -Trigger $backupTrigger -Settings $backupSettings -Principal $backupPrincipal -Description "Respaldo automático de la base de datos PostgreSQL de La Posada del Oso"
    Show-Success "Tarea de respaldo automático configurada: $BackupSchedule los $BackupDay a las $BackupTime"
} catch {
    Show-Error "Error al configurar tarea de respaldo: $($_.Exception.Message)"
}

# 2. Configurar auditoría de seguridad diaria
$securityScriptPath = Join-Path $PWD "security-audit-simple.ps1"
$securityTaskName = "Posada_Auditoria_Seguridad"

if (Test-Path $securityScriptPath) {
    try {
        Unregister-ScheduledTask -TaskName $securityTaskName -Confirm:$false -ErrorAction SilentlyContinue
    } catch {
        # Ignorar si la tarea no existe
    }

    $securityAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -File `"$securityScriptPath`" > `"$reportPath\auditoria-$(Get-Date -Format 'yyyy-MM-dd').txt`""
    $securityTrigger = New-ScheduledTaskTrigger -Daily -At "06:00"
    $securitySettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

    try {
        Register-ScheduledTask -TaskName $securityTaskName -Action $securityAction -Trigger $securityTrigger -Settings $securitySettings -Principal $backupPrincipal -Description "Auditoría diaria de seguridad de La Posada del Oso"
        Show-Success "Tarea de auditoría de seguridad configurada: diaria a las 06:00"
    } catch {
        Show-Warning "Error al configurar auditoría de seguridad: $($_.Exception.Message)"
    }
} else {
    Show-Warning "Script de auditoría de seguridad no encontrado: $securityScriptPath"
}

# 3. Configurar limpieza de logs si está habilitada
if ($EnableLogCleanup) {
    $cleanupTaskName = "Posada_Limpieza_Logs"
    
    try {
        Unregister-ScheduledTask -TaskName $cleanupTaskName -Confirm:$false -ErrorAction SilentlyContinue
    } catch {
        # Ignorar si la tarea no existe
    }

    # Crear script de limpieza inline
    $cleanupScript = @"
# Limpiar logs antiguos (más de 30 días)
Get-ChildItem logs/*.log | Where-Object {`$_.LastWriteTime -lt (Get-Date).AddDays(-30)} | Remove-Item -Force
# Limpiar reportes antiguos (más de 90 días)
Get-ChildItem reportes/*.txt | Where-Object {`$_.LastWriteTime -lt (Get-Date).AddDays(-90)} | Remove-Item -Force
# Limpiar respaldos antiguos según retención configurada
Get-ChildItem backups/posada_backup_* | Where-Object {`$_.LastWriteTime -lt (Get-Date).AddDays(-$RetentionDays)} | Remove-Item -Force
"@

    $cleanupAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -Command `"$cleanupScript`""
    $cleanupTrigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Monday -At "03:00"
    $cleanupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

    try {
        Register-ScheduledTask -TaskName $cleanupTaskName -Action $cleanupAction -Trigger $cleanupTrigger -Settings $cleanupSettings -Principal $backupPrincipal -Description "Limpieza automática de logs y archivos antiguos de La Posada del Oso"
        Show-Success "Tarea de limpieza de logs configurada: semanal los lunes a las 03:00"
    } catch {
        Show-Warning "Error al configurar limpieza de logs: $($_.Exception.Message)"
    }
}

# 4. Configurar monitoreo de base de datos semanal
$monitorScriptPath = Join-Path $PWD "scripts\monitor-database.ps1"
$monitorTaskName = "Posada_Monitoreo_BD"

if (Test-Path $monitorScriptPath) {
    try {
        Unregister-ScheduledTask -TaskName $monitorTaskName -Confirm:$false -ErrorAction SilentlyContinue
    } catch {
        # Ignorar si la tarea no existe
    }

    $monitorAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -File `"$monitorScriptPath`" -Detailed > `"$reportPath\monitoreo-bd-$(Get-Date -Format 'yyyy-MM-dd').txt`""
    $monitorTrigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Saturday -At "08:00"
    $monitorSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

    try {
        Register-ScheduledTask -TaskName $monitorTaskName -Action $monitorAction -Trigger $monitorTrigger -Settings $monitorSettings -Principal $backupPrincipal -Description "Monitoreo semanal detallado de la base de datos PostgreSQL"
        Show-Success "Tarea de monitoreo de BD configurada: semanal los sábados a las 08:00"
    } catch {
        Show-Warning "Error al configurar monitoreo de BD: $($_.Exception.Message)"
    }
} else {
    Show-Warning "Script de monitoreo de BD no encontrado: $monitorScriptPath"
}

Write-Host ""
Show-Info "Verificando tareas programadas configuradas..."

# Listar todas las tareas relacionadas con Posada
$posadaTasks = Get-ScheduledTask | Where-Object { $_.TaskName -like "Posada_*" }

if ($posadaTasks.Count -gt 0) {
    Write-Host ""
    Write-Host "📋 TAREAS PROGRAMADAS CONFIGURADAS:" -ForegroundColor Yellow
    foreach ($task in $posadaTasks) {
        $nextRun = (Get-ScheduledTaskInfo -TaskName $task.TaskName).NextRunTime
        Write-Host "  ✅ $($task.TaskName) - Próxima ejecución: $nextRun" -ForegroundColor Green
    }
} else {
    Show-Warning "No se encontraron tareas programadas de Posada"
}

Write-Host ""
Write-Host "📁 DIRECTORIOS CREADOS:" -ForegroundColor Yellow
Write-Host "  📂 Respaldos: $backupPath"
Write-Host "  📂 Reportes: $reportPath"

Write-Host ""
Show-Success "Configuración de respaldos automáticos completada"
Write-Host ""
Write-Host "🔍 COMANDOS ÚTILES:" -ForegroundColor Cyan
Write-Host "  • Ver tareas: schtasks /query | Select-String 'Posada'"
Write-Host "  • Ejecutar respaldo manual: npm run db:backup"
Write-Host "  • Ver logs: Get-Content logs/database.log -Tail 20"
Write-Host "  • Monitorear BD: npm run db:monitor"
Write-Host ""
Write-Host "⚠️  IMPORTANTE: Las tareas programadas se ejecutarán con tu usuario actual." -ForegroundColor Yellow
Write-Host "   Asegúrate de que tu equipo esté encendido en los horarios programados." -ForegroundColor Yellow
