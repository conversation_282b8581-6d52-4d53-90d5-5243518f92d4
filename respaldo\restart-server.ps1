#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Script para reiniciar automáticamente el servidor de desarrollo.

.DESCRIPTION
    Este script verifica si el puerto 5443 está en uso, termina el proceso
    que lo está utilizando si es necesario, y luego inicia el servidor de
    desarrollo con el comando "npm run dev".

.NOTES
    Autor: Augment Agent
    Fecha: Mayo 2024
#>

# Configuracion de codificacion para caracteres especiales
# Nota: Se han eliminado acentos para evitar problemas de codificacion

# Funcion para mostrar mensajes con formato
function Write-ColorMessage {
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [string]$ForegroundColor = "White"
    )

    Write-Host $Message -ForegroundColor $ForegroundColor
}

# Funcion para manejar errores
function Write-ErrorAndExit {
    param (
        [Parameter(Mandatory = $true)]
        [string]$ErrorMessage
    )

    Write-ColorMessage "ERROR: $ErrorMessage" -ForegroundColor "Red"
    Write-ColorMessage "El script se detendra." -ForegroundColor "Red"
    exit 1
}

# Mostrar encabezado
Write-ColorMessage "=======================================================" -ForegroundColor "Cyan"
Write-ColorMessage "  REINICIO AUTOMATICO DEL SERVIDOR DE DESARROLLO" -ForegroundColor "Cyan"
Write-ColorMessage "=======================================================" -ForegroundColor "Cyan"
Write-ColorMessage "Iniciando proceso de verificacion y reinicio..." -ForegroundColor "Yellow"

try {
    # Paso 1: Verificar si el puerto 5443 esta en uso
    Write-ColorMessage "Verificando si el puerto 5443 esta en uso..." -ForegroundColor "Yellow"
    $portInUse = Get-NetTCPConnection -LocalPort 5443 -ErrorAction SilentlyContinue

    if ($portInUse) {
        # Paso 2: Identificar el PID del proceso que esta utilizando el puerto
        $processPID = $portInUse.OwningProcess
        $processInfo = Get-Process -Id $processPID -ErrorAction SilentlyContinue

        if ($processInfo) {
            Write-ColorMessage "Puerto 5443 esta siendo utilizado por el proceso: $($processInfo.ProcessName) (PID: $processPID)" -ForegroundColor "Magenta"

            # Terminar el proceso
            Write-ColorMessage "Terminando el proceso con PID $processPID..." -ForegroundColor "Yellow"
            Stop-Process -Id $processPID -Force

            # Esperar un momento para asegurarse de que el proceso se haya terminado
            Start-Sleep -Seconds 2

            # Verificar si el proceso se terminó correctamente
            $processStillRunning = Get-Process -Id $processPID -ErrorAction SilentlyContinue

            if ($processStillRunning) {
                Write-ErrorAndExit "No se pudo terminar el proceso con PID $processPID."
            } else {
                Write-ColorMessage "Proceso terminado exitosamente." -ForegroundColor "Green"
            }
        } else {
            Write-ColorMessage "No se pudo obtener informacion del proceso con PID $processPID." -ForegroundColor "Yellow"

            # Intentar terminar el proceso de todos modos
            try {
                Stop-Process -Id $processPID -Force -ErrorAction SilentlyContinue
                Write-ColorMessage "Se intento terminar el proceso con PID $processPID." -ForegroundColor "Yellow"
            } catch {
                Write-ColorMessage "No se pudo terminar el proceso con PID $processPID." -ForegroundColor "Yellow"
            }
        }
    } else {
        Write-ColorMessage "El puerto 5443 esta libre." -ForegroundColor "Green"
    }

    # Verificar nuevamente si el puerto esta libre
    $portStillInUse = Get-NetTCPConnection -LocalPort 5443 -ErrorAction SilentlyContinue

    if ($portStillInUse) {
        Write-ErrorAndExit "El puerto 5443 sigue en uso despues de intentar liberar el proceso. Por favor, revise manualmente."
    }

    # Paso 3: Iniciar el servidor de desarrollo
    Write-ColorMessage "Iniciando el servidor de desarrollo con 'npm run dev'..." -ForegroundColor "Yellow"

    # Iniciar el servidor en un nuevo proceso para que el script pueda continuar
    Start-Process -FilePath "npm" -ArgumentList "run", "dev" -NoNewWindow

    Write-ColorMessage "Servidor iniciado correctamente." -ForegroundColor "Green"
    Write-ColorMessage "El servidor esta disponible en:" -ForegroundColor "Cyan"
    Write-ColorMessage "  - HTTPS: https://localhost:5443" -ForegroundColor "Cyan"
    Write-ColorMessage "  - HTTP:  http://localhost:5080 (redirecciona a HTTPS)" -ForegroundColor "Cyan"

} catch {
    Write-ErrorAndExit "Ocurrio un error inesperado durante la ejecucion del script."
}

Write-ColorMessage "=======================================================" -ForegroundColor "Cyan"
Write-ColorMessage "  PROCESO COMPLETADO" -ForegroundColor "Cyan"
Write-ColorMessage "=======================================================" -ForegroundColor "Cyan"
