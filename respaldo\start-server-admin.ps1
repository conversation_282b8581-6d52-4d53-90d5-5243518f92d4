# Script para iniciar el servidor con permisos elevados
# Este script debe ejecutarse como administrador

Write-Host "=======================================================`n  INICIANDO SERVIDOR CON PERMISOS ELEVADOS`n=======================================================" -ForegroundColor Cyan

# Verificar si estamos ejecutando como administrador
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "Este script debe ejecutarse como administrador. Reiniciando con permisos elevados..." -ForegroundColor Yellow
    
    # Reiniciar el script con permisos elevados
    Start-Process powershell.exe -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
    exit
}

# Limpiar la caché de Vite
Write-Host "Limpiando la caché de Vite..." -ForegroundColor Yellow
Remove-Item -Path "node_modules\.vite" -Recurse -Force -ErrorAction SilentlyContinue

# Verificar si el puerto 5443 está en uso
$portInUse = $null
try {
    $portInUse = Get-NetTCPConnection -LocalPort 5443 -ErrorAction SilentlyContinue
} catch {
    # No hacer nada si hay un error
}

if ($portInUse) {
    Write-Host "El puerto 5443 está en uso. Intentando liberar el puerto..." -ForegroundColor Yellow
    
    foreach ($connection in $portInUse) {
        $process = Get-Process -Id $connection.OwningProcess -ErrorAction SilentlyContinue
        if ($process) {
            Write-Host "Terminando proceso $($process.ProcessName) (ID: $($process.Id))..." -ForegroundColor Yellow
            Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
        }
    }
}

# Iniciar el servidor
Write-Host "Iniciando el servidor de desarrollo..." -ForegroundColor Green
npm run dev

Write-Host "=======================================================`n  PROCESO COMPLETADO`n=======================================================" -ForegroundColor Cyan
