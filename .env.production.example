# =====================================================
# VARIABLES DE ENTORNO PARA PRODUCCIÓN
# =====================================================
# Copia este archivo como .env en el servidor de producción
# y configura los valores apropiados para tu entorno

# =====================================================
# CONFIGURACIÓN DEL SERVIDOR
# =====================================================

# Entorno de ejecución (CRÍTICO: debe ser 'production')
NODE_ENV=production

# Puertos del servidor
PORT=80
HTTPS_PORT=443

# Nivel de logging (error, warn, info, debug)
LOG_LEVEL=warn

# =====================================================
# BASE DE DATOS
# =====================================================

# URL de conexión a PostgreSQL para producción
# IMPORTANTE: Usar SSL y credenciales seguras
# Formato: postgresql://usuario:contraseña@host:puerto/database?sslmode=require
DATABASE_URL=postgresql://posada_user:CONTRASEÑ***********************:5432/posada_production?sslmode=require

# =====================================================
# SEGURIDAD Y SESIONES
# =====================================================

# Clave secreta para sesiones (CRÍTICO: generar una clave única y segura)
# Usar: openssl rand -base64 64
SESSION_SECRET=GENERAR_CLAVE_SECRETA_UNICA_DE_64_CARACTERES_MINIMO

# =====================================================
# CONFIGURACIÓN SSL/TLS
# =====================================================

# Rutas a los certificados SSL para producción
# IMPORTANTE: Usar certificados válidos de una CA reconocida
SSL_KEY_PATH=/etc/ssl/private/posada.key
SSL_CERT_PATH=/etc/ssl/certs/posada.crt

# Ruta al certificado de la cadena (si es necesario)
SSL_CA_PATH=/etc/ssl/certs/ca-bundle.crt

# =====================================================
# CONFIGURACIÓN DE CORS
# =====================================================

# Orígenes permitidos para CORS (separados por comas)
# IMPORTANTE: Especificar solo los dominios necesarios
ALLOWED_ORIGINS=https://tudominio.com,https://www.tudominio.com

# =====================================================
# RATE LIMITING
# =====================================================

# Ventana de tiempo para rate limiting (en milisegundos)
# 15 minutos = 900000ms
RATE_LIMIT_WINDOW_MS=900000

# Máximo número de requests por ventana de tiempo
RATE_LIMIT_MAX_REQUESTS=100

# Rate limiting específico para formularios
FORM_RATE_LIMIT_WINDOW_MS=900000
FORM_RATE_LIMIT_MAX_REQUESTS=5

# Rate limiting específico para autenticación
AUTH_RATE_LIMIT_WINDOW_MS=900000
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# =====================================================
# CONFIGURACIÓN DE EMAIL (si se implementa)
# =====================================================

# Configuración SMTP para envío de emails
# SMTP_HOST=smtp.ejemplo.com
# SMTP_PORT=587
# SMTP_SECURE=true
# SMTP_USER=<EMAIL>
# SMTP_PASS=contraseña_email

# Email de contacto para notificaciones
# CONTACT_EMAIL=<EMAIL>

# =====================================================
# CONFIGURACIÓN DE MONITOREO
# =====================================================

# URL para webhook de alertas de seguridad (opcional)
# SECURITY_WEBHOOK_URL=https://hooks.slack.com/services/...

# Configuración de métricas (opcional)
# METRICS_ENABLED=true
# METRICS_PORT=9090

# =====================================================
# CONFIGURACIÓN DE BACKUP
# =====================================================

# Configuración para backup automático de base de datos
# BACKUP_ENABLED=true
# BACKUP_SCHEDULE=0 2 * * *
# BACKUP_RETENTION_DAYS=30
# BACKUP_S3_BUCKET=posada-backups
# BACKUP_S3_REGION=us-east-1

# =====================================================
# CONFIGURACIÓN DE CDN (si se usa)
# =====================================================

# URL del CDN para assets estáticos
# CDN_URL=https://cdn.tudominio.com

# =====================================================
# CONFIGURACIÓN DE CACHE
# =====================================================

# Configuración de Redis para cache (si se implementa)
# REDIS_URL=redis://usuario:contraseñ*******************:6379

# =====================================================
# CONFIGURACIÓN DE ANALYTICS
# =====================================================

# Google Analytics (si se usa)
# GA_TRACKING_ID=G-XXXXXXXXXX

# =====================================================
# CONFIGURACIÓN DE PAGOS (si se implementa)
# =====================================================

# Stripe para procesamiento de pagos
# STRIPE_PUBLIC_KEY=pk_live_...
# STRIPE_SECRET_KEY=sk_live_...
# STRIPE_WEBHOOK_SECRET=whsec_...

# =====================================================
# CONFIGURACIÓN DE ALMACENAMIENTO
# =====================================================

# AWS S3 para almacenamiento de archivos
# AWS_ACCESS_KEY_ID=AKIA...
# AWS_SECRET_ACCESS_KEY=...
# AWS_REGION=us-east-1
# AWS_S3_BUCKET=posada-uploads

# =====================================================
# CONFIGURACIÓN DE LOGS
# =====================================================

# Configuración de logging externo (opcional)
# LOG_SERVICE_URL=https://logs.ejemplo.com
# LOG_SERVICE_TOKEN=token_del_servicio

# =====================================================
# CONFIGURACIÓN DE HEALTH CHECKS
# =====================================================

# Configuración para health checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PATH=/health

# =====================================================
# NOTAS IMPORTANTES PARA PRODUCCIÓN
# =====================================================

# 1. NUNCA usar valores por defecto en producción
# 2. Generar claves secretas únicas y seguras
# 3. Usar certificados SSL válidos de una CA reconocida
# 4. Configurar firewall para permitir solo puertos necesarios
# 5. Implementar backup automático de base de datos
# 6. Configurar monitoreo y alertas
# 7. Revisar logs de seguridad regularmente
# 8. Mantener dependencias actualizadas
# 9. Realizar auditorías de seguridad periódicas
# 10. Implementar rotación de secretos

# =====================================================
# COMANDOS ÚTILES PARA PRODUCCIÓN
# =====================================================

# Generar clave secreta segura:
# openssl rand -base64 64

# Verificar certificados SSL:
# openssl x509 -in /path/to/cert.crt -text -noout

# Verificar conexión a base de datos:
# psql $DATABASE_URL -c "SELECT version();"

# Verificar configuración de nginx (si se usa):
# nginx -t

# Reiniciar servicios:
# systemctl restart posada
# systemctl restart nginx
