# Archivo de ejemplo para variables de entorno
# Copia este archivo como .env y configura los valores apropiados

# Base de datos
# Para desarrollo (almacenamiento en memoria):
# DATABASE_URL=memory://localhost
# Para PostgreSQL local:
# DATABASE_URL=postgresql://usuario:contraseña@localhost:5432/posada_db
# Para PostgreSQL con SSL (producción):
# DATABASE_URL=postgresql://usuario:contraseña@host:5432/posada_db?sslmode=require
DATABASE_URL=postgresql://usuario:contraseña@localhost:5432/posada_db

# Configuración SSL para PostgreSQL (producción)
# Certificado de autoridad certificadora
DB_SSL_CA=
# Certificado del cliente
DB_SSL_CERT=
# Clave privada del cliente
DB_SSL_KEY=

# Configuración del servidor
NODE_ENV=development
PORT=5080
HTTPS_PORT=5443

# Configuración de sesiones
SESSION_SECRET=tu_clave_secreta_muy_larga_y_segura_aqui

# Configuración SSL (para producción)
SSL_KEY_PATH=./certs/key.pem
SSL_CERT_PATH=./certs/cert.pem

# Configuración de CORS
ALLOWED_ORIGINS=http://localhost:5080,https://localhost:5443

# Configuración de rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Configuración de logging
LOG_LEVEL=info
