--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

-- Started on 2025-05-26 00:07:00

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

DROP DATABASE IF EXISTS posada_db;
--
-- TOC entry 4821 (class 1262 OID 50065)
-- Name: posada_db; Type: DATABASE; Schema: -; Owner: posada_user
--

CREATE DATABASE posada_db WITH TEMPLATE = template0 ENCODING = 'UTF8' LOCALE_PROVIDER = libc LOCALE = 'es-CL';


ALTER DATABASE posada_db OWNER TO posada_user;

\connect posada_db

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- TOC entry 218 (class 1259 OID 50067)
-- Name: bookings; Type: TABLE; Schema: public; Owner: posada_user
--

CREATE TABLE public.bookings (
    id integer NOT NULL,
    check_in date NOT NULL,
    check_out date NOT NULL,
    configuration_type text NOT NULL,
    guests jsonb NOT NULL,
    contact_info jsonb NOT NULL,
    special_requests text,
    pricing jsonb NOT NULL,
    status text DEFAULT 'pending'::text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.bookings OWNER TO posada_user;

--
-- TOC entry 217 (class 1259 OID 50066)
-- Name: bookings_id_seq; Type: SEQUENCE; Schema: public; Owner: posada_user
--

CREATE SEQUENCE public.bookings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.bookings_id_seq OWNER TO posada_user;

--
-- TOC entry 4822 (class 0 OID 0)
-- Dependencies: 217
-- Name: bookings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: posada_user
--

ALTER SEQUENCE public.bookings_id_seq OWNED BY public.bookings.id;


--
-- TOC entry 220 (class 1259 OID 50078)
-- Name: contacts; Type: TABLE; Schema: public; Owner: posada_user
--

CREATE TABLE public.contacts (
    id integer NOT NULL,
    name text NOT NULL,
    email text NOT NULL,
    phone text,
    subject text NOT NULL,
    message text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.contacts OWNER TO posada_user;

--
-- TOC entry 219 (class 1259 OID 50077)
-- Name: contacts_id_seq; Type: SEQUENCE; Schema: public; Owner: posada_user
--

CREATE SEQUENCE public.contacts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.contacts_id_seq OWNER TO posada_user;

--
-- TOC entry 4823 (class 0 OID 0)
-- Dependencies: 219
-- Name: contacts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: posada_user
--

ALTER SEQUENCE public.contacts_id_seq OWNED BY public.contacts.id;


--
-- TOC entry 222 (class 1259 OID 50088)
-- Name: users; Type: TABLE; Schema: public; Owner: posada_user
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username text NOT NULL,
    password text NOT NULL
);


ALTER TABLE public.users OWNER TO posada_user;

--
-- TOC entry 221 (class 1259 OID 50087)
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: posada_user
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO posada_user;

--
-- TOC entry 4824 (class 0 OID 0)
-- Dependencies: 221
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: posada_user
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- TOC entry 4651 (class 2604 OID 50070)
-- Name: bookings id; Type: DEFAULT; Schema: public; Owner: posada_user
--

ALTER TABLE ONLY public.bookings ALTER COLUMN id SET DEFAULT nextval('public.bookings_id_seq'::regclass);


--
-- TOC entry 4654 (class 2604 OID 50081)
-- Name: contacts id; Type: DEFAULT; Schema: public; Owner: posada_user
--

ALTER TABLE ONLY public.contacts ALTER COLUMN id SET DEFAULT nextval('public.contacts_id_seq'::regclass);


--
-- TOC entry 4656 (class 2604 OID 50091)
-- Name: users id; Type: DEFAULT; Schema: public; Owner: posada_user
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- TOC entry 4811 (class 0 OID 50067)
-- Dependencies: 218
-- Data for Name: bookings; Type: TABLE DATA; Schema: public; Owner: posada_user
--

COPY public.bookings (id, check_in, check_out, configuration_type, guests, contact_info, special_requests, pricing, status, created_at) FROM stdin;
\.


--
-- TOC entry 4813 (class 0 OID 50078)
-- Dependencies: 220
-- Data for Name: contacts; Type: TABLE DATA; Schema: public; Owner: posada_user
--

COPY public.contacts (id, name, email, phone, subject, message, created_at) FROM stdin;
\.


--
-- TOC entry 4815 (class 0 OID 50088)
-- Dependencies: 222
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: posada_user
--

COPY public.users (id, username, password) FROM stdin;
\.


--
-- TOC entry 4825 (class 0 OID 0)
-- Dependencies: 217
-- Name: bookings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: posada_user
--

SELECT pg_catalog.setval('public.bookings_id_seq', 1, false);


--
-- TOC entry 4826 (class 0 OID 0)
-- Dependencies: 219
-- Name: contacts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: posada_user
--

SELECT pg_catalog.setval('public.contacts_id_seq', 1, false);


--
-- TOC entry 4827 (class 0 OID 0)
-- Dependencies: 221
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: posada_user
--

SELECT pg_catalog.setval('public.users_id_seq', 1, false);


--
-- TOC entry 4658 (class 2606 OID 50076)
-- Name: bookings bookings_pkey; Type: CONSTRAINT; Schema: public; Owner: posada_user
--

ALTER TABLE ONLY public.bookings
    ADD CONSTRAINT bookings_pkey PRIMARY KEY (id);


--
-- TOC entry 4660 (class 2606 OID 50086)
-- Name: contacts contacts_pkey; Type: CONSTRAINT; Schema: public; Owner: posada_user
--

ALTER TABLE ONLY public.contacts
    ADD CONSTRAINT contacts_pkey PRIMARY KEY (id);


--
-- TOC entry 4662 (class 2606 OID 50095)
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: posada_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- TOC entry 4664 (class 2606 OID 50097)
-- Name: users users_username_unique; Type: CONSTRAINT; Schema: public; Owner: posada_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_unique UNIQUE (username);


-- Completed on 2025-05-26 00:07:03

--
-- PostgreSQL database dump complete
--

