# Definir la ruta base
$basePath = "client\src\assets\atracciones"

# Carpetas antiguas a eliminar
$oldFolders = @(
    "Pueblo-de-pica",
    "Reserva-Pampa-del-Tamarugal",
    "Salar-del-Huasco"
)

# Eliminar las carpetas antiguas
foreach ($folder in $oldFolders) {
    $folderPath = Join-Path -Path $basePath -ChildPath $folder
    
    if (Test-Path $folderPath) {
        Write-Host "Eliminando carpeta '$folder'..." -ForegroundColor Yellow
        Remove-Item -Path $folderPath -Recurse -Force
        Write-Host "Carpeta '$folder' eliminada exitosamente." -ForegroundColor Green
    } else {
        Write-Host "La carpeta '$folder' no existe." -ForegroundColor Yellow
    }
}

Write-Host "Proceso completado." -ForegroundColor Cyan
