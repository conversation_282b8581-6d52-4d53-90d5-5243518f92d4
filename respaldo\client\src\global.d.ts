// global.d.ts

interface Window {
  tailwind?: {
    config?: {
      theme?: {
        extend?: {
          colors?: Record<string, any>;
          fontFamily?: Record<string, any>;
          height?: Record<string, any>;
          animation?: Record<string, any>;
          keyframes?: Record<string, any>;
          // Agrega aquí otras propiedades que extiendas en tu tema de Tailwind
        };
      };
    };
  };
}