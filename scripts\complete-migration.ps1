# Script para completar la migracion a PostgreSQL
# Autor: Sistema de Gestion Posada 2.0

param(
    [string]$DatabaseUrl = "",
    [switch]$SkipBackup = $false
)

Write-Host ""
Write-Host "COMPLETAR MIGRACION A POSTGRESQL" -ForegroundColor Cyan
Write-Host "=" * 40 -ForegroundColor Cyan

# Verificar que estamos en el directorio correcto
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: Este script debe ejecutarse desde el directorio raiz del proyecto" -ForegroundColor Red
    exit 1
}

# Si se proporciona una URL personalizada, actualizarla
if ($DatabaseUrl -ne "") {
    Write-Host "Actualizando DATABASE_URL personalizada..." -ForegroundColor Cyan
    
    # Crear respaldo del .env actual
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
    Copy-Item ".env" ".env.backup.$timestamp"
    
    # Leer contenido actual del .env
    $envContent = Get-Content ".env"
    
    # Actualizar DATABASE_URL
    $newEnvContent = $envContent | ForEach-Object {
        if ($_ -match "^DATABASE_URL=") {
            "DATABASE_URL=$DatabaseUrl"
        } else {
            $_
        }
    }
    
    $newEnvContent | Set-Content ".env"
    Write-Host "DATABASE_URL actualizada: $DatabaseUrl" -ForegroundColor Green
} else {
    # Usar la URL por defecto configurada
    Write-Host "Usando DATABASE_URL por defecto de desarrollo..." -ForegroundColor Cyan
    
    # Actualizar .env con la URL de PostgreSQL por defecto
    $envContent = Get-Content ".env"
    $newEnvContent = $envContent | ForEach-Object {
        if ($_ -match "^DATABASE_URL=") {
            "DATABASE_URL=postgresql://posada_user:posada_password@localhost:5432/posada_db"
        } else {
            $_
        }
    }
    
    $newEnvContent | Set-Content ".env"
    Write-Host "DATABASE_URL configurada para PostgreSQL local" -ForegroundColor Green
}

Write-Host ""
Write-Host "PASO 4: MIGRACION DEL ESQUEMA" -ForegroundColor Yellow

Write-Host "Ejecutando migracion del esquema..." -ForegroundColor Cyan
try {
    npm run db:push
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Migracion del esquema COMPLETADA" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Fallo en la migracion del esquema" -ForegroundColor Red
        Write-Host "Verificar que PostgreSQL este ejecutandose y las credenciales sean correctas" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "PASO 5: VERIFICACION POST-MIGRACION" -ForegroundColor Yellow

Write-Host "Verificando conexion a PostgreSQL..." -ForegroundColor Cyan
npm run db:verify

Write-Host ""
Write-Host "Monitoreando estado de la base de datos..." -ForegroundColor Cyan
npm run db:monitor

Write-Host ""
Write-Host "PASO 6: RESPALDO POST-MIGRACION" -ForegroundColor Yellow

if (-not $SkipBackup) {
    Write-Host "Creando respaldo de PostgreSQL..." -ForegroundColor Cyan
    npm run db:backup
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Respaldo post-migracion COMPLETADO" -ForegroundColor Green
    } else {
        Write-Host "ADVERTENCIA: Fallo en el respaldo post-migracion" -ForegroundColor Yellow
    }
} else {
    Write-Host "Respaldo omitido (parametro -SkipBackup)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "MIGRACION COMPLETADA EXITOSAMENTE" -ForegroundColor Green
Write-Host "=" * 40 -ForegroundColor Green

Write-Host ""
Write-Host "RESUMEN DE LA MIGRACION:" -ForegroundColor Cyan
Write-Host "1. Respaldo preventivo: COMPLETADO" -ForegroundColor Green
Write-Host "2. Verificacion pre-migracion: COMPLETADO" -ForegroundColor Green
Write-Host "3. Configuracion DATABASE_URL: COMPLETADO" -ForegroundColor Green
Write-Host "4. Migracion del esquema: COMPLETADO" -ForegroundColor Green
Write-Host "5. Verificacion post-migracion: COMPLETADO" -ForegroundColor Green
Write-Host "6. Respaldo post-migracion: COMPLETADO" -ForegroundColor Green

Write-Host ""
Write-Host "PROXIMOS PASOS RECOMENDADOS:" -ForegroundColor Yellow
Write-Host "1. Probar la aplicacion: npm run dev"
Write-Host "2. Verificar que los datos se persisten al reiniciar"
Write-Host "3. Configurar respaldos automaticos: npm run db:setup-backups"
Write-Host "4. Monitorear regularmente: npm run db:monitor"

Write-Host ""
Write-Host "ARCHIVOS DE RESPALDO CREADOS:" -ForegroundColor Cyan
$backups = Get-ChildItem ".env.backup.*", "backups/config_backup_*", "backups/posada_backup_*" -ErrorAction SilentlyContinue
foreach ($backup in $backups) {
    Write-Host "- $($backup.Name)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "La aplicacion ahora usa PostgreSQL como base de datos principal." -ForegroundColor Green
Write-Host "Los datos se persistiran entre reinicios de la aplicacion." -ForegroundColor Green
