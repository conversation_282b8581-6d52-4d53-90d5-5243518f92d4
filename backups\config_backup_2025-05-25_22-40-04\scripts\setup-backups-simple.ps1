# Script simple para configurar respaldos automaticos
# Autor: Sistema de Gestion Posada 2.0

Write-Host ""
Write-Host "CONFIGURACION DE RESPALDOS AUTOMATICOS" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Verificar que estamos en el directorio correcto
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: Este script debe ejecutarse desde el directorio raiz del proyecto" -ForegroundColor Red
    exit 1
}

# Crear directorio de respaldos si no existe
$backupPath = Join-Path $PWD "backups"
if (-not (Test-Path $backupPath)) {
    New-Item -Path $backupPath -ItemType Directory -Force | Out-Null
    Write-Host "Directorio de respaldos creado: $backupPath" -ForegroundColor Green
}

# Crear directorio de reportes si no existe
$reportPath = Join-Path $PWD "reportes"
if (-not (Test-Path $reportPath)) {
    New-Item -Path $reportPath -ItemType Directory -Force | Out-Null
    Write-Host "Directorio de reportes creado: $reportPath" -ForegroundColor Green
}

Write-Host "Configurando tareas programadas..." -ForegroundColor Cyan

# 1. Configurar respaldo automatico de base de datos
$backupScriptPath = Join-Path $PWD "scripts\backup-database.ps1"
$backupTaskName = "Posada_Backup_Automatico"

# Eliminar tarea existente si existe
try {
    Unregister-ScheduledTask -TaskName $backupTaskName -Confirm:$false -ErrorAction SilentlyContinue
} catch {
    # Ignorar si la tarea no existe
}

# Crear nueva tarea de respaldo
$backupAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -File `"$backupScriptPath`""
$backupTrigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Sunday -At "02:00"
$backupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
$backupPrincipal = New-ScheduledTaskPrincipal -UserId $env:USERNAME -LogonType Interactive

try {
    Register-ScheduledTask -TaskName $backupTaskName -Action $backupAction -Trigger $backupTrigger -Settings $backupSettings -Principal $backupPrincipal -Description "Respaldo automatico de la base de datos PostgreSQL de La Posada del Oso"
    Write-Host "Tarea de respaldo automatico configurada: Domingos a las 02:00" -ForegroundColor Green
} catch {
    Write-Host "Error al configurar tarea de respaldo: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Configurar auditoria de seguridad diaria
$securityScriptPath = Join-Path $PWD "security-audit-simple.ps1"
$securityTaskName = "Posada_Auditoria_Seguridad"

if (Test-Path $securityScriptPath) {
    try {
        Unregister-ScheduledTask -TaskName $securityTaskName -Confirm:$false -ErrorAction SilentlyContinue
    } catch {
        # Ignorar si la tarea no existe
    }

    $securityAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -File `"$securityScriptPath`""
    $securityTrigger = New-ScheduledTaskTrigger -Daily -At "06:00"
    $securitySettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

    try {
        Register-ScheduledTask -TaskName $securityTaskName -Action $securityAction -Trigger $securityTrigger -Settings $securitySettings -Principal $backupPrincipal -Description "Auditoria diaria de seguridad de La Posada del Oso"
        Write-Host "Tarea de auditoria de seguridad configurada: diaria a las 06:00" -ForegroundColor Green
    } catch {
        Write-Host "Error al configurar auditoria de seguridad: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "Script de auditoria de seguridad no encontrado: $securityScriptPath" -ForegroundColor Yellow
}

# 3. Configurar limpieza de logs
$cleanupTaskName = "Posada_Limpieza_Logs"

try {
    Unregister-ScheduledTask -TaskName $cleanupTaskName -Confirm:$false -ErrorAction SilentlyContinue
} catch {
    # Ignorar si la tarea no existe
}

# Crear script de limpieza inline
$cleanupScript = "Get-ChildItem logs/*.log | Where-Object {`$_.LastWriteTime -lt (Get-Date).AddDays(-30)} | Remove-Item -Force; Get-ChildItem reportes/*.txt | Where-Object {`$_.LastWriteTime -lt (Get-Date).AddDays(-90)} | Remove-Item -Force"

$cleanupAction = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-ExecutionPolicy Bypass -Command `"$cleanupScript`""
$cleanupTrigger = New-ScheduledTaskTrigger -Weekly -DaysOfWeek Monday -At "03:00"
$cleanupSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable

try {
    Register-ScheduledTask -TaskName $cleanupTaskName -Action $cleanupAction -Trigger $cleanupTrigger -Settings $cleanupSettings -Principal $backupPrincipal -Description "Limpieza automatica de logs y archivos antiguos de La Posada del Oso"
    Write-Host "Tarea de limpieza de logs configurada: lunes a las 03:00" -ForegroundColor Green
} catch {
    Write-Host "Error al configurar limpieza de logs: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Verificando tareas programadas configuradas..." -ForegroundColor Cyan

# Listar todas las tareas relacionadas con Posada
$posadaTasks = Get-ScheduledTask | Where-Object { $_.TaskName -like "Posada_*" }

if ($posadaTasks.Count -gt 0) {
    Write-Host ""
    Write-Host "TAREAS PROGRAMADAS CONFIGURADAS:" -ForegroundColor Yellow
    foreach ($task in $posadaTasks) {
        $nextRun = (Get-ScheduledTaskInfo -TaskName $task.TaskName).NextRunTime
        Write-Host "  $($task.TaskName) - Proxima ejecucion: $nextRun" -ForegroundColor Green
    }
} else {
    Write-Host "No se encontraron tareas programadas de Posada" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "DIRECTORIOS CREADOS:" -ForegroundColor Yellow
Write-Host "  Respaldos: $backupPath"
Write-Host "  Reportes: $reportPath"

Write-Host ""
Write-Host "Configuracion de respaldos automaticos completada" -ForegroundColor Green
Write-Host ""
Write-Host "COMANDOS UTILES:" -ForegroundColor Cyan
Write-Host "  • Ver tareas: schtasks /query | Select-String 'Posada'"
Write-Host "  • Ejecutar respaldo manual: npm run db:backup"
Write-Host "  • Verificar estado: npm run db:verify"
Write-Host ""
Write-Host "IMPORTANTE: Las tareas programadas se ejecutaran con tu usuario actual." -ForegroundColor Yellow
Write-Host "Asegurate de que tu equipo este encendido en los horarios programados." -ForegroundColor Yellow
