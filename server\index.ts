import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import fs from "fs";
import path from "path";
import https from "https";
import helmet from "helmet";
import rateLimit from "express-rate-limit";
import cors from "cors";
import dotenv from "dotenv";
import session from "express-session";
import ConnectPgSimple from "connect-pg-simple";
import { loadUser } from "./auth";
import { initializeSecurityLogging, securityLoggingMiddleware, logRateLimitHit } from "./security-logger";
import { conditionalCSRFProtection, csrfTokenMiddleware } from "./csrf-protection";
import { initializeSecurityMonitoring, securityMonitoringMiddleware } from "./security-monitor";

// Cargar variables de entorno
dotenv.config();

const app = express();

// Inicializar sistemas de seguridad
initializeSecurityLogging();
initializeSecurityMonitoring();

// Configurar CORS
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:5080', 'https://localhost:5443'],
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Configuración de sesiones
const PgSession = ConnectPgSimple(session);
const sessionConfig: session.SessionOptions = {
  store: process.env.DATABASE_URL && !process.env.DATABASE_URL.startsWith('memory://')
    ? new PgSession({
        conString: process.env.DATABASE_URL,
        tableName: 'session', // tabla para almacenar sesiones
        createTableIfMissing: true, // crear tabla automáticamente
      })
    : undefined, // usar MemoryStore por defecto si no hay PostgreSQL
  secret: process.env.SESSION_SECRET || 'fallback-secret-key-change-in-production',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production', // HTTPS en producción
    httpOnly: true, // prevenir acceso desde JavaScript
    maxAge: 24 * 60 * 60 * 1000, // 24 horas
    sameSite: 'strict' // protección CSRF
  },
  name: 'posada.sid' // nombre personalizado para la cookie de sesión
};

app.use(session(sessionConfig));

// Middleware para cargar usuario autenticado en todas las requests
app.use(loadUser);

// Middleware de logging de seguridad
app.use(securityLoggingMiddleware());

// Protección CSRF
app.use(conditionalCSRFProtection());
app.use(csrfTokenMiddleware());

// Monitoreo de seguridad en tiempo real
app.use(securityMonitoringMiddleware());

// Configurar Helmet para headers de seguridad
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://cdn.tailwindcss.com", "https://fonts.googleapis.com"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.tailwindcss.com"],
      imgSrc: ["'self'", "data:", "blob:", "https:"],
      fontSrc: ["'self'", "data:", "https://fonts.gstatic.com"],
      connectSrc: ["'self'"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      workerSrc: ["'self'", "blob:"],
    },
  },
  crossOriginEmbedderPolicy: false, // Deshabilitado para compatibilidad con desarrollo
}));

// Rate limiting con logging de seguridad
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutos por defecto
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // 100 requests por ventana por defecto
  message: {
    error: 'Demasiadas solicitudes desde esta IP, intenta nuevamente más tarde.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req: Request, res: Response) => {
    logRateLimitHit(req, 'general_api');
    res.status(429).json({
      error: 'Demasiadas solicitudes desde esta IP, intenta nuevamente más tarde.'
    });
  },
});

// Aplicar rate limiting a rutas API
app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));

// Middleware para redirigir HTTP a HTTPS en producción
app.use((req, res, next) => {
  if (app.get('env') === 'production' && !req.secure) {
    // Verificar si la solicitud es segura (HTTPS)
    // En algunos entornos, como detrás de un proxy, req.secure podría no funcionar correctamente
    // En ese caso, puedes verificar el encabezado X-Forwarded-Proto
    const isSecure = req.secure || req.headers['x-forwarded-proto'] === 'https';

    if (!isSecure && req.method === 'GET') {
      // En desarrollo, redirigir a la página de ayuda para certificados
      if (app.get('env') === 'development' && req.url !== '/cert-help') {
        // Redirigir a HTTPS (usando el puerto 5443)
        const host = req.headers.host?.split(':')[0] || 'localhost';
        return res.redirect(301, `https://${host}:5443/cert-help`);
      } else {
        // Redirigir a HTTPS (usando el puerto 5443)
        const host = req.headers.host?.split(':')[0] || 'localhost';
        return res.redirect(301, `https://${host}:5443${req.url}`);
      }
    }
  }
  next();
});

// Headers de seguridad adicionales (Helmet ya maneja la mayoría)
app.use((req, res, next) => {
  // Headers adicionales específicos para la aplicación
  res.setHeader('X-Powered-By', 'La Posada del Oso');
  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Configuración para HTTPS
  const httpsOptions = {
    key: fs.readFileSync(path.join(process.cwd(), 'certs/key.pem')),
    cert: fs.readFileSync(path.join(process.cwd(), 'certs/cert.pem')),
    // En desarrollo, no rechazamos conexiones no autorizadas
    // Esto es útil para certificados autofirmados
    rejectUnauthorized: app.get('env') !== 'development'
  };

  // Puertos para los servidores
  const httpsPort = 5443; // Puerto estándar alternativo para HTTPS
  const httpPort = 5080;  // Puerto alternativo para HTTP

  // Crear servidor HTTPS
  const httpsServer = https.createServer(httpsOptions, app);

  httpsServer.listen(httpsPort, "0.0.0.0", () => {
    log(`Servidor HTTPS ejecutándose en https://localhost:${httpsPort}`);
  });

  // También mantenemos el servidor HTTP para redireccionar a HTTPS
  server.listen(httpPort, "0.0.0.0", () => {
    log(`Servidor HTTP ejecutándose en http://localhost:${httpPort} (redirecciona a HTTPS)`);
  });
})();
