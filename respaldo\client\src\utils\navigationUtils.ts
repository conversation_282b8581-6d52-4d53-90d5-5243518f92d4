/**
 * Utilidades para navegación y direcciones con Google Maps
 */

// Coordenadas fijas de la cabaña en Pica
export const CABIN_COORDINATES = {
  lat: -20.494676,
  lng: -69.3265558,
  address: "La Banda, Lote 9, Sitio 13, Pica, Chile"
};

export interface Coordinates {
  lat: number;
  lng: number;
}

/**
 * Genera una URL de Google Maps con direcciones desde la cabaña hasta un destino específico
 * @param destination - Coordenadas del destino
 * @param destinationName - Nombre del destino (opcional, para mejor UX)
 * @returns URL de Google Maps con la ruta trazada
 */
export function generateDirectionsUrl(
  destination: Coordinates,
  destinationName?: string
): string {
  const origin = `${CABIN_COORDINATES.lat},${CABIN_COORDINATES.lng}`;
  const dest = `${destination.lat},${destination.lng}`;

  // URL base de Google Maps para direcciones
  const baseUrl = "https://www.google.com/maps/dir/";

  // Construir la URL con origen y destino
  const url = `${baseUrl}${origin}/${dest}`;

  // Agregar parámetros adicionales para mejorar la experiencia
  const params = new URLSearchParams({
    // Forzar el uso de la interfaz web de Google Maps
    'hl': 'es', // Idioma español
    'gl': 'CL', // Región Chile
    // Modo de transporte por defecto (driving)
    'travelmode': 'driving'
  });

  return `${url}?${params.toString()}`;
}

/**
 * Genera una URL de Google Maps que muestra solo la ubicación del destino
 * @param destination - Coordenadas del destino
 * @param destinationName - Nombre del destino
 * @returns URL de Google Maps centrada en el destino
 */
export function generateLocationUrl(
  destination: Coordinates,
  destinationName?: string
): string {
  const baseUrl = "https://www.google.com/maps/search/";
  const location = `${destination.lat},${destination.lng}`;

  const params = new URLSearchParams({
    'api': '1',
    'query': destinationName || location,
    'hl': 'es',
    'gl': 'CL'
  });

  return `${baseUrl}?${params.toString()}`;
}

/**
 * Abre Google Maps con direcciones en una nueva pestaña
 * @param destination - Coordenadas del destino
 * @param destinationName - Nombre del destino (opcional)
 */
export function openDirections(
  destination: Coordinates,
  destinationName?: string
): void {
  const url = generateDirectionsUrl(destination, destinationName);
  window.open(url, '_blank', 'noopener,noreferrer');
}

/**
 * Detecta si el usuario está en un dispositivo móvil
 * @returns true si es un dispositivo móvil
 */
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );
}

/**
 * Genera una URL optimizada para dispositivos móviles que abre la app de Google Maps
 * @param destination - Coordenadas del destino
 * @param destinationName - Nombre del destino (opcional)
 * @returns URL optimizada para móviles
 */
export function generateMobileDirectionsUrl(
  destination: Coordinates,
  destinationName?: string
): string {
  const origin = `${CABIN_COORDINATES.lat},${CABIN_COORDINATES.lng}`;
  const dest = `${destination.lat},${destination.lng}`;

  // URL para abrir la app de Google Maps en móviles
  return `https://maps.google.com/?saddr=${origin}&daddr=${dest}&directionsmode=driving`;
}

/**
 * Abre direcciones optimizadas según el tipo de dispositivo
 * @param destination - Coordenadas del destino
 * @param destinationName - Nombre del destino (opcional)
 */
export function openOptimizedDirections(
  destination: Coordinates,
  destinationName?: string
): void {
  const url = isMobileDevice()
    ? generateMobileDirectionsUrl(destination, destinationName)
    : generateDirectionsUrl(destination, destinationName);

  window.open(url, '_blank', 'noopener,noreferrer');
}
