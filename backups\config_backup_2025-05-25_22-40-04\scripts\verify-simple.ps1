# Script simple de verificacion PostgreSQL
# Autor: Sistema de Gestion Posada 2.0

Write-Host ""
Write-Host "VERIFICACION DE INTEGRACION POSTGRESQL" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Verificar que estamos en el directorio correcto
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: Este script debe ejecutarse desde el directorio raiz del proyecto" -ForegroundColor Red
    exit 1
}

$issues = @()
$recommendations = @()

# 1. Verificar archivo .env
Write-Host "Verificando configuracion de variables de entorno..." -ForegroundColor Cyan

if (Test-Path ".env") {
    $envContent = Get-Content ".env"
    $databaseUrl = $envContent | Where-Object { $_ -match "^DATABASE_URL=" }
    
    if ($databaseUrl) {
        $dbUrlValue = ($databaseUrl -split "=", 2)[1]
        Write-Host "  DATABASE_URL encontrado: $dbUrlValue"
        
        if ($dbUrlValue -match "memory://") {
            Write-Host "  ESTADO: Actualmente usando almacenamiento en memoria" -ForegroundColor Yellow
            $issues += "DATABASE_URL configurado para memoria en lugar de PostgreSQL"
            $recommendations += "Cambiar DATABASE_URL a PostgreSQL"
        } elseif ($dbUrlValue -match "postgresql://") {
            Write-Host "  ESTADO: DATABASE_URL configurado para PostgreSQL" -ForegroundColor Green
        } else {
            Write-Host "  ERROR: Formato de DATABASE_URL no reconocido" -ForegroundColor Red
            $issues += "DATABASE_URL tiene formato invalido"
        }
    } else {
        Write-Host "  ERROR: DATABASE_URL no encontrado en .env" -ForegroundColor Red
        $issues += "DATABASE_URL no esta configurado"
    }
} else {
    Write-Host "  ERROR: Archivo .env no encontrado" -ForegroundColor Red
    $issues += "Archivo .env no existe"
    $recommendations += "Crear archivo .env basado en .env.example"
}

# 2. Verificar herramientas de PostgreSQL
Write-Host "Verificando herramientas de PostgreSQL..." -ForegroundColor Cyan

$pgTools = @("psql", "pg_dump", "pg_restore")
$missingTools = @()

foreach ($tool in $pgTools) {
    if (Get-Command $tool -ErrorAction SilentlyContinue) {
        Write-Host "  $tool disponible" -ForegroundColor Green
    } else {
        Write-Host "  $tool no encontrado" -ForegroundColor Yellow
        $missingTools += $tool
    }
}

if ($missingTools.Count -gt 0) {
    $issues += "Herramientas de PostgreSQL faltantes: $($missingTools -join ', ')"
    $recommendations += "Instalar PostgreSQL client tools"
}

# 3. Verificar scripts de gestion
Write-Host "Verificando scripts de gestion..." -ForegroundColor Cyan

$scripts = @{
    "scripts/backup-database.ps1" = "Respaldo de base de datos"
    "scripts/restore-database.ps1" = "Restauracion de base de datos"
    "scripts/monitor-database.ps1" = "Monitoreo de base de datos"
}

foreach ($script in $scripts.Keys) {
    if (Test-Path $script) {
        Write-Host "  $($scripts[$script]) disponible" -ForegroundColor Green
    } else {
        Write-Host "  $($scripts[$script]) no encontrado" -ForegroundColor Yellow
        $issues += "Script faltante: $script"
    }
}

# 4. Verificar directorio de respaldos
Write-Host "Verificando configuracion de respaldos..." -ForegroundColor Cyan

if (Test-Path "backups") {
    Write-Host "  Directorio de respaldos existe" -ForegroundColor Green
    
    $backups = Get-ChildItem "backups/posada_backup_*.sql" -ErrorAction SilentlyContinue
    if ($backups) {
        Write-Host "    Respaldos encontrados: $($backups.Count)"
        $latestBackup = $backups | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        Write-Host "    Ultimo respaldo: $($latestBackup.LastWriteTime)"
    } else {
        Write-Host "  No se encontraron respaldos" -ForegroundColor Yellow
        $recommendations += "Ejecutar 'npm run db:backup' para crear primer respaldo"
    }
} else {
    Write-Host "  Directorio de respaldos no existe" -ForegroundColor Yellow
    $recommendations += "Crear directorio 'backups' para almacenar respaldos"
}

# 5. Verificar package.json scripts
Write-Host "Verificando scripts de npm..." -ForegroundColor Cyan

$packageJson = Get-Content "package.json" | ConvertFrom-Json
$expectedScripts = @("db:backup", "db:restore", "db:monitor", "db:push")

foreach ($script in $expectedScripts) {
    if ($packageJson.scripts.$script) {
        Write-Host "  Script '$script' configurado" -ForegroundColor Green
    } else {
        Write-Host "  Script '$script' no encontrado" -ForegroundColor Yellow
        $issues += "Script npm faltante: $script"
    }
}

# Mostrar resumen
Write-Host ""
Write-Host "RESUMEN DE VERIFICACION" -ForegroundColor Yellow
Write-Host "=" * 30 -ForegroundColor Yellow

if ($issues.Count -eq 0) {
    Write-Host "No se encontraron problemas criticos" -ForegroundColor Green
} else {
    Write-Host "Se encontraron $($issues.Count) problema(s):" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "    • $issue" -ForegroundColor Red
    }
}

if ($recommendations.Count -gt 0) {
    Write-Host ""
    Write-Host "RECOMENDACIONES:" -ForegroundColor Cyan
    foreach ($rec in $recommendations) {
        Write-Host "    • $rec" -ForegroundColor Yellow
    }
}

Write-Host ""
if ($issues.Count -eq 0) {
    Write-Host "Integracion PostgreSQL verificada exitosamente" -ForegroundColor Green
} else {
    Write-Host "Revisa los problemas identificados antes de continuar" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "COMANDOS UTILES:" -ForegroundColor Cyan
Write-Host "  • Migrar esquema: npm run db:push"
Write-Host "  • Crear respaldo: npm run db:backup"
Write-Host "  • Configurar respaldos automaticos: npm run db:setup-backups"
Write-Host "  • Migrar desde memoria: npm run db:migrate-from-memory"
