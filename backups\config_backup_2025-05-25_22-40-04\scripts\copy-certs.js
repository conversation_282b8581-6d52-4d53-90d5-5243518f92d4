// Script para copiar certificados SSL al directorio de distribución
import fs from 'fs';
import path from 'path';

const certsDir = path.join(process.cwd(), 'certs');
const distCertsDir = path.join(process.cwd(), 'dist', 'certs');

// Crear directorio de certificados en dist si no existe
if (!fs.existsSync(distCertsDir)) {
  console.log('Creando directorio de certificados en dist...');
  fs.mkdirSync(distCertsDir, { recursive: true });
}

// Copiar certificados
try {
  console.log('Copiando certificados SSL...');
  
  // Copiar key.pem
  fs.copyFileSync(
    path.join(certsDir, 'key.pem'),
    path.join(distCertsDir, 'key.pem')
  );
  
  // Copiar cert.pem
  fs.copyFileSync(
    path.join(certsDir, 'cert.pem'),
    path.join(distCertsDir, 'cert.pem')
  );
  
  console.log('Certificados copiados correctamente.');
} catch (error) {
  console.error('Error al copiar certificados:', error.message);
  process.exit(1);
}
