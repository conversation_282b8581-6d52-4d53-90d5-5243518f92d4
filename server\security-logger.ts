import winston from 'winston';
import path from 'path';
import { Request } from 'express';

// Configuración del logger de seguridad
const securityLogger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
      return JSON.stringify({
        timestamp,
        level,
        message,
        ...meta
      });
    })
  ),
  defaultMeta: { service: 'posada-security' },
  transports: [
    // Archivo para logs de seguridad
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'security.log'),
      level: 'info',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    }),
    // Archivo para logs de errores críticos
    new winston.transports.File({
      filename: path.join(process.cwd(), 'logs', 'security-error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true
    })
  ]
});

// En desarrollo, también mostrar en consola
if (process.env.NODE_ENV !== 'production') {
  securityLogger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
        return `${timestamp} [${level}]: ${message} ${metaStr}`;
      })
    )
  }));
}

// Tipos para eventos de seguridad
export interface SecurityEvent {
  type: 'auth' | 'validation' | 'rate_limit' | 'csrf' | 'access' | 'error' | 'suspicious';
  action: string;
  ip?: string;
  userAgent?: string;
  userId?: number;
  username?: string;
  endpoint?: string;
  method?: string;
  statusCode?: number;
  details?: any;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

// Función para extraer información de la request
function extractRequestInfo(req: Request) {
  return {
    ip: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent') || 'unknown',
    endpoint: req.path,
    method: req.method,
    userId: req.session?.userId,
    username: req.user?.username
  };
}

// Funciones específicas para diferentes tipos de eventos de seguridad

export function logAuthEvent(req: Request, action: string, details?: any, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium') {
  const event: SecurityEvent = {
    type: 'auth',
    action,
    severity,
    details,
    ...extractRequestInfo(req)
  };

  const level = severity === 'critical' ? 'error' : severity === 'high' ? 'warn' : 'info';
  securityLogger.log(level, `Auth Event: ${action}`, event);
}

export function logValidationError(req: Request, errors: any[], severity: 'low' | 'medium' | 'high' = 'medium') {
  const event: SecurityEvent = {
    type: 'validation',
    action: 'validation_failed',
    severity,
    details: { errors, body: req.body },
    ...extractRequestInfo(req)
  };

  securityLogger.warn(`Validation Error: ${errors.length} validation errors`, event);
}

export function logRateLimitHit(req: Request, limitType: string) {
  const event: SecurityEvent = {
    type: 'rate_limit',
    action: 'rate_limit_exceeded',
    severity: 'high',
    details: { limitType },
    ...extractRequestInfo(req)
  };

  securityLogger.warn(`Rate Limit Exceeded: ${limitType}`, event);
}

export function logCSRFAttempt(req: Request, details?: any) {
  const event: SecurityEvent = {
    type: 'csrf',
    action: 'csrf_token_invalid',
    severity: 'high',
    details,
    ...extractRequestInfo(req)
  };

  securityLogger.warn('CSRF Attack Attempt', event);
}

export function logSuspiciousActivity(req: Request, action: string, details?: any, severity: 'medium' | 'high' | 'critical' = 'high') {
  const event: SecurityEvent = {
    type: 'suspicious',
    action,
    severity,
    details,
    ...extractRequestInfo(req)
  };

  const level = severity === 'critical' ? 'error' : 'warn';
  securityLogger.log(level, `Suspicious Activity: ${action}`, event);
}

export function logAccessEvent(req: Request, statusCode: number, details?: any) {
  const event: SecurityEvent = {
    type: 'access',
    action: 'api_access',
    statusCode,
    severity: statusCode >= 400 ? (statusCode >= 500 ? 'high' : 'medium') : 'low',
    details,
    ...extractRequestInfo(req)
  };

  const level = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';
  securityLogger.log(level, `API Access: ${req.method} ${req.path} - ${statusCode}`, event);
}

export function logSecurityError(error: Error, req?: Request, details?: any) {
  const event: SecurityEvent = {
    type: 'error',
    action: 'security_error',
    severity: 'critical',
    details: {
      error: error.message,
      stack: error.stack,
      ...details
    },
    ...(req ? extractRequestInfo(req) : {})
  };

  securityLogger.error(`Security Error: ${error.message}`, event);
}

// Middleware para logging automático de accesos
export function securityLoggingMiddleware() {
  return (req: Request, res: any, next: any) => {
    // Capturar el código de estado cuando la respuesta termine
    const originalSend = res.send;
    res.send = function(body: any) {
      // Solo loggear rutas de API para evitar spam
      if (req.path.startsWith('/api')) {
        logAccessEvent(req, res.statusCode, { responseSize: body?.length });
      }
      return originalSend.call(this, body);
    };

    next();
  };
}

// Función para crear directorio de logs si no existe
export async function initializeSecurityLogging() {
  const fs = await import('fs');
  const logsDir = path.join(process.cwd(), 'logs');
  
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  securityLogger.info('Security logging system initialized', {
    type: 'system',
    action: 'logging_initialized',
    severity: 'low',
    details: { logsDirectory: logsDir }
  });
}

export default securityLogger;
