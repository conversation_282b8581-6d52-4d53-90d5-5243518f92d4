# Script de Verificacion de Seguridad - La Posada del Oso
# Descripcion: Verifica el estado de seguridad del proyecto

Write-Host "VERIFICACION DE SEGURIDAD - LA POSADA DEL OSO" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Cyan
Write-Host ""

# 1. Verificar dependencias vulnerables
Write-Host "Verificando vulnerabilidades de dependencias..." -ForegroundColor Yellow
try {
    npm audit --json > audit-temp.json 2>$null
    if (Test-Path "audit-temp.json") {
        $auditContent = Get-Content "audit-temp.json" -Raw
        if ($auditContent -and $auditContent.Trim() -ne "") {
            $auditResult = $auditContent | ConvertFrom-Json
            if ($auditResult.metadata -and $auditResult.metadata.vulnerabilities) {
                $vulnerabilities = $auditResult.metadata.vulnerabilities
                Write-Host "Total vulnerabilidades: $($vulnerabilities.total)" -ForegroundColor Red
                Write-Host "Criticas: $($vulnerabilities.critical)" -ForegroundColor Red
                Write-Host "Altas: $($vulnerabilities.high)" -ForegroundColor Yellow
                Write-Host "Moderadas: $($vulnerabilities.moderate)" -ForegroundColor Yellow
            }
        }
        Remove-Item "audit-temp.json" -ErrorAction SilentlyContinue
    }
} catch {
    Write-Host "Error al verificar dependencias" -ForegroundColor Red
}
Write-Host ""

# 2. Verificar archivos de configuracion
Write-Host "Verificando configuracion de seguridad..." -ForegroundColor Yellow

if (Test-Path ".env.example") {
    Write-Host "OK: Archivo .env.example presente" -ForegroundColor Green
} else {
    Write-Host "FALTA: Archivo .env.example" -ForegroundColor Red
}

if (Test-Path ".gitignore") {
    $gitignoreContent = Get-Content ".gitignore" -Raw
    if ($gitignoreContent -match "\.env") {
        Write-Host "OK: Variables de entorno excluidas de Git" -ForegroundColor Green
    } else {
        Write-Host "FALTA: Variables de entorno no excluidas de Git" -ForegroundColor Red
    }

    if ($gitignoreContent -match "certs/") {
        Write-Host "OK: Certificados excluidos de Git" -ForegroundColor Green
    } else {
        Write-Host "FALTA: Certificados no excluidos de Git" -ForegroundColor Red
    }
} else {
    Write-Host "FALTA: Archivo .gitignore" -ForegroundColor Red
}
Write-Host ""

# 3. Verificar certificados SSL
Write-Host "Verificando certificados SSL..." -ForegroundColor Yellow
if (Test-Path "certs/cert.pem") {
    Write-Host "OK: Certificado SSL presente" -ForegroundColor Green
} else {
    Write-Host "FALTA: Certificado SSL" -ForegroundColor Red
}

if (Test-Path "certs/key.pem") {
    Write-Host "OK: Clave privada SSL presente" -ForegroundColor Green
} else {
    Write-Host "FALTA: Clave privada SSL" -ForegroundColor Red
}
Write-Host ""

# 4. Verificar compilacion TypeScript
Write-Host "Verificando compilacion TypeScript..." -ForegroundColor Yellow
try {
    $tscResult = npm run check 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "OK: Compilacion TypeScript exitosa" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Compilacion TypeScript fallida" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: No se pudo verificar TypeScript" -ForegroundColor Red
}
Write-Host ""

# 5. Verificar documentacion
Write-Host "Verificando documentacion..." -ForegroundColor Yellow
if (Test-Path "seguridad.md") {
    Write-Host "OK: Documentacion de seguridad presente" -ForegroundColor Green
} else {
    Write-Host "FALTA: Documentacion de seguridad" -ForegroundColor Red
}
Write-Host ""

# Resumen final
Write-Host "RESUMEN DE SEGURIDAD" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan
Write-Host ""
Write-Host "CRITICO: Resolver vulnerabilidades de dependencias antes de produccion" -ForegroundColor Red
Write-Host "IMPORTANTE: Configurar variables de entorno para produccion" -ForegroundColor Yellow
Write-Host "COMPLETADO: Headers de seguridad, rate limiting, validacion implementados" -ForegroundColor Green
Write-Host ""
Write-Host "Consulta seguridad.md para mas detalles y proximos pasos" -ForegroundColor White
Write-Host ""
